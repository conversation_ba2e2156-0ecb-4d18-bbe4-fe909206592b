import { useNavigate } from "@tanstack/react-router";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { blogService } from "@/services/blog-service";
import { BlogPostForm } from "@/features/blog-management/components/blog-post-form";
import type { BlogFormValues } from "@/features/blog-management/components/blog-post-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Route } from "@/routes/_authenticated/blog/$blogId/edit";
import { Loading } from "@/components/ui/loading";

export function EditBlogPostPage() {
  const { blogId } = Route.useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data: blog, isLoading: isLoadingBlog } = useQuery({
    queryKey: ["blog", blogId],
    queryFn: () => blogService.getBlogPost(blogId),
  });

  const updateMutation = useMutation({
    mutationFn: (data: BlogFormValues) => {
      return blogService.updateBlogPost(blogId, {
        ...data,
        authorID: blog?.authorID || "",
        publishedAt: data.publishedAt || null,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["blog", blogId] });
      queryClient.invalidateQueries({ queryKey: ["blogPosts"] });
      toast({
        title: "Thành công",
        description: "Bài viết đã được cập nhật thành công.",
        variant: "success",
      });
      navigate({ to: `/blog/${blogId}` });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật bài viết. Vui lòng thử lại sau.",
        variant: "destructive",
      });
      console.error("Error updating blog post:", error);
    },
  });

  const handleSubmit = (data: BlogFormValues) => {
    updateMutation.mutate(data);
  };

  if (isLoadingBlog) {
    return (
      <Loading />
    );
  }

  if (!blog) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold text-red-600">Không tìm thấy bài viết</h2>
        <p className="text-gray-600">
          Bài viết bạn đang tìm không tồn tại hoặc đã bị xóa.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => navigate({ to: "/blog" })}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
        </Button>
      </div>
    );
  }

  const defaultValues: Partial<BlogFormValues> = {
    title: blog.title,
    content: blog.content,
    featuredImage: blog.featuredImage || "",
    tags: blog.tags || "",
    status: blog.status,
    publishedAt: blog.publishedAt || null,
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => navigate({ to: `/blog/${blogId}` })}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Chỉnh sửa bài viết</CardTitle>
        </CardHeader>
        <CardContent>
          <BlogPostForm
            defaultValues={defaultValues}
            onSubmit={handleSubmit}
            isSubmitting={updateMutation.isPending}
          />
        </CardContent>
      </Card>
    </div>
  );
} 