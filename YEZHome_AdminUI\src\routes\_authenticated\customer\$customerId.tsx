import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import { z } from 'zod'
import userService from '@/services/user-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Calendar, User, Phone, Mail, CreditCard, FileText, Building, MapPin, Wallet, Star, Eye, Heart, Clock, TrendingUp, BarChart3 } from 'lucide-react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"
import { Spinner } from '@/components/ui/spinner'
import { Progress } from '@/components/ui/progress'

// Define search schema for the detail page
const customerDetailSearchSchema = z.object({
  from: z.string().optional(),
  filters: z.string().optional(),
})

export const Route = createFileRoute('/_authenticated/customer/$customerId')({
  validateSearch: customerDetailSearchSchema,
  loader: async ({ params }) => {
    return await userService.getUserById(params.customerId);
  },
  beforeLoad: () => {
    return {
      getTitle: () => 'Chi tiết khách hàng'
    };
  },
  component: CustomerDetail,
  pendingComponent: () => <div className="p-10 flex justify-center"><Spinner /></div>,
  errorComponent: () => <div className="p-10">Đã xảy ra lỗi khi tải dữ liệu.</div>,
})

function CustomerDetail() {
  const customerData = Route.useLoaderData();
  const navigate = useNavigate();
  const search = Route.useSearch();
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const params = Route.useParams();
  
  // Fetch customer dashboard data
  const { data: customerDashboard, isLoading: isLoadingDashboard } = useQuery({
    queryKey: ['customerDashboard', params.customerId],
    queryFn: () => userService.getUserDashboard(params.customerId),
  });

  // Fetch customer invoice info
  const { data: customerInvoiceInfo, isLoading: isLoadingInvoiceInfo } = useQuery({
    queryKey: ['customerInvoiceInfo', params.customerId],
    queryFn: () => userService.getUserInvoiceInfo(params.customerId),
  });
  
  // Mutation for updating customer status
  const updateStatusMutation = useMutation({
    mutationFn: async (isActive: boolean) => {
      return await userService.updateUserStatus(params.customerId, { isActive });
    },
    onSuccess: () => {
      // Invalidate and refetch customer data
      queryClient.invalidateQueries({ queryKey: ['customer', params.customerId] });
      
      // Show success toast
      toast({
        title: "Cập nhật trạng thái thành công",
        description: "Trạng thái khách hàng đã được cập nhật",
        variant: "success",
      });
      
      // Close dialog
      setIsStatusDialogOpen(false);
      
      // Force refresh the current route to get updated data
      navigate({ to: `/customer/${params.customerId}`, replace: true });
    },
    onError: (error) => {
      // Show error toast
      toast({
        title: "Lỗi cập nhật trạng thái",
        description: "Đã xảy ra lỗi khi cập nhật trạng thái khách hàng",
        variant: "destructive",
      });
    }
  });
  
  // Handle status toggle
  const toggleStatus = () => {
    updateStatusMutation.mutate(!customerData.isActive);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => {
            // If we came from customer list and have filters, restore them
            if (search.from === 'customer' && search.filters) {
              try {
                const filters = JSON.parse(search.filters);
                navigate({
                  to: '/customer',
                  search: {
                    page: filters.PageNumber || 1,
                    pageSize: filters.PageSize || 10,
                    email: filters.Email || '',
                    name: filters.Name || '',
                    phone: filters.Phone || '',
                    sortColumn: filters.SortColumn,
                    sortDescending: filters.SortDescending,
                  }
                });
              } catch (error) {
                console.error('Error parsing filters:', error);
                navigate({ to: '/customer' });
              }
            } else {
              navigate({ to: '/customer' });
            }
          }}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Chi tiết khách hàng</h1>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={customerData.isActive ? "success" : "destructive"} className="px-3 py-1 text-sm">
            {customerData.isActive ? 'Đang hoạt động' : 'Đã vô hiệu hóa'}
          </Badge>
          <Button 
            variant={customerData.isActive ? "destructive" : "default"}
            onClick={() => setIsStatusDialogOpen(true)}
          >
            {customerData.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'}
          </Button>
        </div>
      </div>

      {/* Customer basic info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Thông tin cá nhân</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Họ tên:</span>
                <span>{customerData.fullName || 'Chưa cập nhật'}</span>
              </div>

              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Email:</span>
                <span>{customerData.email || 'Chưa cập nhật'}</span>
              </div>

              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Số điện thoại:</span>
                <span>{customerData.phone || 'Chưa cập nhật'}</span>
              </div>

              {customerData.phone2 && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Số điện thoại 2:</span>
                  <span>{customerData.phone2}</span>
                </div>
              )}

              {customerData.phone3 && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Số điện thoại 3:</span>
                  <span>{customerData.phone3}</span>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Loại người dùng:</span>
                <span>{customerData.userType || 'Chưa xác định'}</span>
              </div>

              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Mã số thuế cá nhân:</span>
                <span>{customerData.personalTaxCode || 'Chưa cập nhật'}</span>
              </div>

              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Mã chuyển khoản:</span>
                <span>{customerData.transferCode || 'Chưa cập nhật'}</span>
              </div>

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Đăng nhập cuối:</span>
                <span>
                  {customerData.lastLogin 
                    ? new Date(customerData.lastLogin).toLocaleString('vi-VN') 
                    : 'Chưa đăng nhập'}
                </span>
              </div>

              {customerDashboard?.userInfo?.createdAt && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Ngày tham gia:</span>
                  <span>
                    {new Date(customerDashboard.userInfo.createdAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Wallet Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Thông tin ví tiền</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingDashboard ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Wallet className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Số dư:</span>
                  <span className="font-bold text-lg">
                    {customerDashboard?.walletInfo?.balance !== undefined
                      ? formatCurrency(customerDashboard.walletInfo.balance)
                      : '0 ₫'}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Tổng chi tiêu:</span>
                  <span className="font-bold text-lg">
                    {customerDashboard?.walletInfo?.totalSpent !== undefined
                      ? formatCurrency(customerDashboard.walletInfo.totalSpent)
                      : '0 ₫'}
                  </span>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Tổng số giao dịch:</span>
                  <span>{customerDashboard?.walletInfo?.totalTransactions || 0}</span>
                </div>

                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Chi tiêu tháng trước:</span>
                  <span>
                    {customerDashboard?.walletInfo?.lastMonthSpending !== undefined
                      ? formatCurrency(customerDashboard.walletInfo.lastMonthSpending)
                      : '0 ₫'}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Member Ranking */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Hạng thành viên</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingDashboard ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : customerDashboard?.memberRanking ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="font-semibold">Hạng hiện tại:</span>
                  <Badge variant="outline" className="text-sm capitalize">
                    {customerDashboard.memberRanking.currentRank || 'Chưa xác định'}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-blue-500" />
                  <span className="font-semibold">Hạng tiếp theo:</span>
                  <Badge variant="outline" className="text-sm capitalize">
                    {customerDashboard.memberRanking.nextRank || 'Cao nhất'}
                  </Badge>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>
                    {formatCurrency(customerDashboard.memberRanking.minSpent)}
                  </span>
                  <span>
                    {formatCurrency(customerDashboard.memberRanking.maxSpent)}
                  </span>
                </div>
                <Progress value={customerDashboard.memberRanking.progressPercentage} className="h-2" />
                <div className="text-sm text-center">
                  Cần chi tiêu thêm {formatCurrency(customerDashboard.memberRanking.spendingToNextRank)} để lên hạng {customerDashboard.memberRanking.nextRank}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              Không có thông tin hạng thành viên
            </div>
          )}
        </CardContent>
      </Card>

      {/* Property Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Thống kê bất động sản</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingDashboard ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : customerDashboard?.propertyStats ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <div className="bg-muted rounded-lg p-4 text-center">
                <Building className="h-6 w-6 mx-auto mb-2 text-primary" />
                <div className="text-2xl font-bold">{customerDashboard.propertyStats.totalProperties}</div>
                <div className="text-sm text-muted-foreground">Tổng số BĐS</div>
              </div>
              
              <div className="bg-muted rounded-lg p-4 text-center">
                <Building className="h-6 w-6 mx-auto mb-2 text-green-500" />
                <div className="text-2xl font-bold">{customerDashboard.propertyStats.activeProperties}</div>
                <div className="text-sm text-muted-foreground">BĐS đang hoạt động</div>
              </div>
              
              <div className="bg-muted rounded-lg p-4 text-center">
                <Clock className="h-6 w-6 mx-auto mb-2 text-yellow-500" />
                <div className="text-2xl font-bold">{customerDashboard.propertyStats.expiredProperties}</div>
                <div className="text-sm text-muted-foreground">BĐS hết hạn</div>
              </div>
              
              <div className="bg-muted rounded-lg p-4 text-center">
                <FileText className="h-6 w-6 mx-auto mb-2 text-blue-500" />
                <div className="text-2xl font-bold">{customerDashboard.propertyStats.draftProperties}</div>
                <div className="text-sm text-muted-foreground">BĐS nháp</div>
              </div>
              
              <div className="bg-muted rounded-lg p-4 text-center">
                <Heart className="h-6 w-6 mx-auto mb-2 text-red-500" />
                <div className="text-2xl font-bold">{customerDashboard.propertyStats.favoriteProperties}</div>
                <div className="text-sm text-muted-foreground">BĐS yêu thích</div>
              </div>
              
              <div className="bg-muted rounded-lg p-4 text-center">
                <Eye className="h-6 w-6 mx-auto mb-2 text-purple-500" />
                <div className="text-2xl font-bold">{customerDashboard.propertyStats.totalViews}</div>
                <div className="text-sm text-muted-foreground">Lượt xem</div>
              </div>
              
              <div className="bg-muted rounded-lg p-4 text-center">
                <Star className="h-6 w-6 mx-auto mb-2 text-amber-500" />
                <div className="text-2xl font-bold">{customerDashboard.propertyStats.averageRating || 0}</div>
                <div className="text-sm text-muted-foreground">Đánh giá trung bình</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              Không có thống kê bất động sản
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      {customerDashboard?.recentTransactions && customerDashboard.recentTransactions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Giao dịch gần đây</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-muted">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Ngày</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Loại</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Mô tả</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">Số tiền</th>
                  </tr>
                </thead>
                <tbody className="bg-card divide-y divide-gray-200">
                  {customerDashboard.recentTransactions.map((transaction, index) => (
                    <tr key={index}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                        {transaction.date ? new Date(transaction.date).toLocaleDateString('vi-VN') : '-'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                        {transaction.type || '-'}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {transaction.description || '-'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                        {transaction.amount !== undefined ? formatCurrency(transaction.amount) : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Invoice Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Thông tin xuất hóa đơn</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingInvoiceInfo ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Tên người mua:</span>
                  <span>{customerInvoiceInfo?.buyerName || 'Chưa cập nhật'}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Email:</span>
                  <span>{customerInvoiceInfo?.email || 'Chưa cập nhật'}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Tên công ty:</span>
                  <span>{customerInvoiceInfo?.companyName || 'Chưa cập nhật'}</span>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Mã số thuế:</span>
                  <span>{customerInvoiceInfo?.taxCode || 'Chưa cập nhật'}</span>
                </div>
              </div>

              <div className="col-span-1 md:col-span-2 space-y-2">
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                  <span className="font-semibold">Địa chỉ:</span>
                  <span className="flex-1">{customerInvoiceInfo?.address || 'Chưa cập nhật'}</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Update Dialog */}
      <AlertDialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {customerData.isActive 
                ? 'Xác nhận vô hiệu hóa tài khoản' 
                : 'Xác nhận kích hoạt tài khoản'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {customerData.isActive
                ? 'Bạn có chắc chắn muốn vô hiệu hóa tài khoản này? Người dùng sẽ không thể đăng nhập cho đến khi được kích hoạt lại.'
                : 'Bạn có chắc chắn muốn kích hoạt tài khoản này? Người dùng sẽ có thể đăng nhập và sử dụng dịch vụ.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={toggleStatus}
              className={customerData.isActive ? "bg-red-600 hover:bg-red-700" : ""}
            >
              {customerData.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 