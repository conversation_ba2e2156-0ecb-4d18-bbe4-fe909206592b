{"name": ".", "private": true, "type": "module", "scripts": {"dev": "vite --port 4000", "start": "vite --port 4000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "lint": "eslint", "format": "prettier", "check": "prettier --write . && eslint --fix"}, "dependencies": {"@faker-js/faker": "^9.6.0", "@goongmaps/goong-js": "^1.0.9", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.0.6", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-query": "^5.66.5", "@tanstack/react-query-devtools": "^5.66.5", "@tanstack/react-router": "^1.121.2", "@tanstack/react-router-devtools": "^1.121.2", "@tanstack/react-table": "^8.21.2", "@tanstack/router-plugin": "^1.121.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.476.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-photo-album": "^3.1.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.6", "tailwindcss-animate": "^1.0.7", "yet-another-react-lightbox": "^3.24.0", "zod": "^4.0.2", "zustand": "^5.0.6"}, "devDependencies": {"@tanstack/eslint-config": "^0.1.0", "@tanstack/router-cli": "^1.125.4", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/node": "^24.0.13", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "prettier": "^3.5.3", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}