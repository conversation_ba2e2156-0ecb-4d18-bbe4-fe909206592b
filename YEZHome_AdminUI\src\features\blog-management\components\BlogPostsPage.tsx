import { useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { blogService } from "@/services/blog-service";
import { BlogPostsTable } from "@/features/blog-management/components/blog-posts-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Loading } from "@/components/ui/loading";

export function BlogPostsPage() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortColumn, setSortColumn] = useState<string | undefined>(undefined);
  const [sortDescending, setSortDescending] = useState<boolean | undefined>(undefined);
  const navigate = useNavigate();

  const { data, isLoading, error } = useQuery({
    queryKey: ["blogPosts", page, pageSize, searchQuery, sortColumn, sortDescending],
    queryFn: () =>
      blogService.getBlogPosts({
        PageNumber: page,
        PageSize: pageSize,
        SearchQuery: searchQuery || undefined,
        SortColumn: sortColumn,
        SortDescending: sortDescending,
      }),
  });

  const handlePaginationChange = (newPage: number, newPageSize: number) => {
    setPage(newPage);
    setPageSize(newPageSize);
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setPage(1); // Reset to first page when searching
  };

  const handleSortChange = (field: string, order: "asc" | "desc") => {
    setSortColumn(field);
    setSortDescending(order === "desc"); // Convert "desc" to true, "asc" to false
  };

  if (isLoading) {
    return (
      <Loading />
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold text-red-600">Đã xảy ra lỗi</h2>
        <p className="text-gray-600">
          Không thể tải danh sách bài viết. Vui lòng thử lại sau.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Quản lý bài viết</CardTitle>
          <Button onClick={() => navigate({ to: "/blog/new" })}>
            <Plus className="mr-2 h-4 w-4" /> Tạo bài viết mới
          </Button>
        </CardHeader>
        <CardContent>
          <BlogPostsTable
            data={data?.items || []}
            pageCount={data?.pageCount || 0}
            onPaginationChange={handlePaginationChange}
            currentPage={page}
            pageSize={pageSize}
            onSearchChange={handleSearchChange}
            onSortChange={handleSortChange}
          />
        </CardContent>
      </Card>
    </div>
  );
} 