{"version": 3, "sources": ["../../yet-another-react-lightbox/dist/plugins/zoom/index.js"], "sourcesContent": ["import * as React from 'react';\nimport { useLightboxProps, useMotionPreference, useEventCallback, useLayoutEffect, useLightboxState, isImageSlide, isImageFitCover, round, useDocumentContext, useController, usePointerEvents, cleanup, makeUseContext, createIcon, IconButton, devicePixelRatio, ImageSlide, clsx, cssClass, addToolbarButton, createModule } from '../../index.js';\nimport { EVENT_ON_KEY_DOWN, EVENT_ON_WHEEL, UNKNOWN_ACTION_TYPE, CLASS_FULLSIZE, CLASS_FLEX_CENTER, CLASS_SLIDE_WRAPPER, CLASS_SLIDE_WRAPPER_INTERACTIVE, PLUGIN_ZOOM } from '../../types.js';\n\nconst defaultZoomProps = {\n    maxZoomPixelRatio: 1,\n    zoomInMultiplier: 2,\n    doubleTapDelay: 300,\n    doubleClickDelay: 500,\n    doubleClickMaxStops: 2,\n    keyboardMoveDistance: 50,\n    wheelZoomDistanceFactor: 100,\n    pinchZoomDistanceFactor: 100,\n    scrollToZoom: false,\n};\nconst resolveZoomProps = (zoom) => ({\n    ...defaultZoomProps,\n    ...zoom,\n});\n\nfunction useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef) {\n    const zoomAnimation = React.useRef(undefined);\n    const zoomAnimationStart = React.useRef(undefined);\n    const { zoom: zoomAnimationDuration } = useLightboxProps().animation;\n    const reduceMotion = useMotionPreference();\n    const playZoomAnimation = useEventCallback(() => {\n        var _a, _b, _c;\n        (_a = zoomAnimation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        zoomAnimation.current = undefined;\n        if (zoomAnimationStart.current && (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)) {\n            try {\n                zoomAnimation.current = (_c = (_b = zoomWrapperRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, [\n                    { transform: zoomAnimationStart.current },\n                    { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` },\n                ], {\n                    duration: !reduceMotion ? (zoomAnimationDuration !== null && zoomAnimationDuration !== void 0 ? zoomAnimationDuration : 500) : 0,\n                    easing: zoomAnimation.current ? \"ease-out\" : \"ease-in-out\",\n                });\n            }\n            catch (err) {\n                console.error(err);\n            }\n            zoomAnimationStart.current = undefined;\n            if (zoomAnimation.current) {\n                zoomAnimation.current.onfinish = () => {\n                    zoomAnimation.current = undefined;\n                };\n            }\n        }\n    });\n    useLayoutEffect(playZoomAnimation, [zoom, offsetX, offsetY, playZoomAnimation]);\n    return React.useCallback(() => {\n        zoomAnimationStart.current = (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)\n            ? window.getComputedStyle(zoomWrapperRef.current).transform\n            : undefined;\n    }, [zoomWrapperRef]);\n}\n\nfunction useZoomCallback(zoom, disabled) {\n    const { on } = useLightboxProps();\n    const onZoomCallback = useEventCallback(() => {\n        var _a;\n        if (!disabled) {\n            (_a = on.zoom) === null || _a === void 0 ? void 0 : _a.call(on, { zoom });\n        }\n    });\n    React.useEffect(onZoomCallback, [zoom, onZoomCallback]);\n}\n\nfunction useZoomProps() {\n    const { zoom } = useLightboxProps();\n    return resolveZoomProps(zoom);\n}\n\nfunction useZoomImageRect(slideRect, imageDimensions) {\n    var _a, _b;\n    let imageRect = { width: 0, height: 0 };\n    let maxImageRect = { width: 0, height: 0 };\n    const { currentSlide } = useLightboxState();\n    const { imageFit } = useLightboxProps().carousel;\n    const { maxZoomPixelRatio } = useZoomProps();\n    if (slideRect && currentSlide) {\n        const slide = { ...currentSlide, ...imageDimensions };\n        if (isImageSlide(slide)) {\n            const cover = isImageFitCover(slide, imageFit);\n            const width = Math.max(...(((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.map((x) => x.width)) || []).concat(slide.width ? [slide.width] : []));\n            const height = Math.max(...(((_b = slide.srcSet) === null || _b === void 0 ? void 0 : _b.map((x) => x.height)) || []).concat(slide.height ? [slide.height] : []));\n            if (width > 0 && height > 0 && slideRect.width > 0 && slideRect.height > 0) {\n                maxImageRect = cover\n                    ? {\n                        width: Math.round(Math.min(width, (slideRect.width / slideRect.height) * height)),\n                        height: Math.round(Math.min(height, (slideRect.height / slideRect.width) * width)),\n                    }\n                    : { width, height };\n                maxImageRect = {\n                    width: maxImageRect.width * maxZoomPixelRatio,\n                    height: maxImageRect.height * maxZoomPixelRatio,\n                };\n                imageRect = cover\n                    ? {\n                        width: Math.min(slideRect.width, maxImageRect.width, width),\n                        height: Math.min(slideRect.height, maxImageRect.height, height),\n                    }\n                    : {\n                        width: Math.round(Math.min(slideRect.width, (slideRect.height / height) * width, width)),\n                        height: Math.round(Math.min(slideRect.height, (slideRect.width / width) * height, height)),\n                    };\n            }\n        }\n    }\n    const maxZoom = imageRect.width ? Math.max(round(maxImageRect.width / imageRect.width, 5), 1) : 1;\n    return { imageRect, maxZoom };\n}\n\nfunction distance(pointerA, pointerB) {\n    return ((pointerA.clientX - pointerB.clientX) ** 2 + (pointerA.clientY - pointerB.clientY) ** 2) ** 0.5;\n}\nfunction scaleZoom(value, delta, factor = 100, clamp = 2) {\n    return value * Math.min(1 + Math.abs(delta / factor), clamp) ** Math.sign(delta);\n}\nfunction useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapperRef) {\n    const activePointers = React.useRef([]);\n    const lastPointerDown = React.useRef(0);\n    const pinchZoomDistance = React.useRef(undefined);\n    const { globalIndex } = useLightboxState();\n    const { getOwnerWindow } = useDocumentContext();\n    const { containerRef, subscribeSensors } = useController();\n    const { keyboardMoveDistance, zoomInMultiplier, wheelZoomDistanceFactor, scrollToZoom, doubleTapDelay, doubleClickDelay, doubleClickMaxStops, pinchZoomDistanceFactor, } = useZoomProps();\n    const translateCoordinates = React.useCallback((event) => {\n        if (containerRef.current) {\n            const { pageX, pageY } = event;\n            const { scrollX, scrollY } = getOwnerWindow();\n            const { left, top, width, height } = containerRef.current.getBoundingClientRect();\n            return [pageX - left - scrollX - width / 2, pageY - top - scrollY - height / 2];\n        }\n        return [];\n    }, [containerRef, getOwnerWindow]);\n    const onKeyDown = useEventCallback((event) => {\n        const { key, metaKey, ctrlKey } = event;\n        const meta = metaKey || ctrlKey;\n        const preventDefault = () => {\n            event.preventDefault();\n            event.stopPropagation();\n        };\n        if (zoom > 1) {\n            const move = (deltaX, deltaY) => {\n                preventDefault();\n                changeOffsets(deltaX, deltaY);\n            };\n            if (key === \"ArrowDown\") {\n                move(0, keyboardMoveDistance);\n            }\n            else if (key === \"ArrowUp\") {\n                move(0, -keyboardMoveDistance);\n            }\n            else if (key === \"ArrowLeft\") {\n                move(-keyboardMoveDistance, 0);\n            }\n            else if (key === \"ArrowRight\") {\n                move(keyboardMoveDistance, 0);\n            }\n        }\n        const handleChangeZoom = (zoomValue) => {\n            preventDefault();\n            changeZoom(zoomValue);\n        };\n        if (key === \"+\" || (meta && key === \"=\")) {\n            handleChangeZoom(zoom * zoomInMultiplier);\n        }\n        else if (key === \"-\" || (meta && key === \"_\")) {\n            handleChangeZoom(zoom / zoomInMultiplier);\n        }\n        else if (meta && key === \"0\") {\n            handleChangeZoom(1);\n        }\n    });\n    const onWheel = useEventCallback((event) => {\n        if (event.ctrlKey || scrollToZoom) {\n            if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n                event.stopPropagation();\n                changeZoom(scaleZoom(zoom, -event.deltaY, wheelZoomDistanceFactor), true, ...translateCoordinates(event));\n                return;\n            }\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (!scrollToZoom) {\n                changeOffsets(event.deltaX, event.deltaY);\n            }\n        }\n    });\n    const clearPointer = React.useCallback((event) => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length, ...pointers.filter((p) => p.pointerId !== event.pointerId));\n    }, []);\n    const replacePointer = React.useCallback((event) => {\n        clearPointer(event);\n        event.persist();\n        activePointers.current.push(event);\n    }, [clearPointer]);\n    const onPointerDown = useEventCallback((event) => {\n        var _a;\n        const pointers = activePointers.current;\n        if ((event.pointerType === \"mouse\" && event.buttons > 1) ||\n            !((_a = zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current) === null || _a === void 0 ? void 0 : _a.contains(event.target))) {\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n        }\n        const { timeStamp } = event;\n        if (pointers.length === 0 &&\n            timeStamp - lastPointerDown.current < (event.pointerType === \"touch\" ? doubleTapDelay : doubleClickDelay)) {\n            lastPointerDown.current = 0;\n            changeZoom(zoom !== maxZoom ? zoom * Math.max(maxZoom ** (1 / doubleClickMaxStops), zoomInMultiplier) : 1, false, ...translateCoordinates(event));\n        }\n        else {\n            lastPointerDown.current = timeStamp;\n        }\n        replacePointer(event);\n        if (pointers.length === 2) {\n            pinchZoomDistance.current = distance(pointers[0], pointers[1]);\n        }\n    });\n    const onPointerMove = useEventCallback((event) => {\n        const pointers = activePointers.current;\n        const activePointer = pointers.find((p) => p.pointerId === event.pointerId);\n        if (pointers.length === 2 && pinchZoomDistance.current) {\n            event.stopPropagation();\n            replacePointer(event);\n            const currentDistance = distance(pointers[0], pointers[1]);\n            const delta = currentDistance - pinchZoomDistance.current;\n            if (Math.abs(delta) > 0) {\n                changeZoom(scaleZoom(zoom, delta, pinchZoomDistanceFactor), true, ...pointers\n                    .map((x) => translateCoordinates(x))\n                    .reduce((acc, coordinate) => coordinate.map((x, i) => acc[i] + x / 2)));\n                pinchZoomDistance.current = currentDistance;\n            }\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (activePointer) {\n                if (pointers.length === 1) {\n                    changeOffsets((activePointer.clientX - event.clientX) / zoom, (activePointer.clientY - event.clientY) / zoom);\n                }\n                replacePointer(event);\n            }\n        }\n    });\n    const onPointerUp = React.useCallback((event) => {\n        const pointers = activePointers.current;\n        if (pointers.length === 2 && pointers.find((p) => p.pointerId === event.pointerId)) {\n            pinchZoomDistance.current = undefined;\n        }\n        clearPointer(event);\n    }, [clearPointer]);\n    const cleanupSensors = React.useCallback(() => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length);\n        lastPointerDown.current = 0;\n        pinchZoomDistance.current = undefined;\n    }, []);\n    usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled);\n    React.useEffect(cleanupSensors, [globalIndex, cleanupSensors]);\n    React.useEffect(() => {\n        if (!disabled) {\n            return cleanup(cleanupSensors, subscribeSensors(EVENT_ON_KEY_DOWN, onKeyDown), subscribeSensors(EVENT_ON_WHEEL, onWheel));\n        }\n        return () => { };\n    }, [disabled, subscribeSensors, cleanupSensors, onKeyDown, onWheel]);\n}\n\nfunction useZoomState(imageRect, maxZoom, zoomWrapperRef) {\n    const [zoom, setZoom] = React.useState(1);\n    const [offsetX, setOffsetX] = React.useState(0);\n    const [offsetY, setOffsetY] = React.useState(0);\n    const animate = useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef);\n    const { currentSlide, globalIndex } = useLightboxState();\n    const { containerRect, slideRect } = useController();\n    const { zoomInMultiplier } = useZoomProps();\n    const currentSource = currentSlide && isImageSlide(currentSlide) ? currentSlide.src : undefined;\n    const disabled = !currentSource || !(zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current);\n    useLayoutEffect(() => {\n        setZoom(1);\n        setOffsetX(0);\n        setOffsetY(0);\n    }, [globalIndex, currentSource]);\n    const changeOffsets = React.useCallback((dx, dy, targetZoom) => {\n        const newZoom = targetZoom || zoom;\n        const newOffsetX = offsetX - (dx || 0);\n        const newOffsetY = offsetY - (dy || 0);\n        const maxOffsetX = (imageRect.width * newZoom - slideRect.width) / 2 / newZoom;\n        const maxOffsetY = (imageRect.height * newZoom - slideRect.height) / 2 / newZoom;\n        setOffsetX(Math.min(Math.abs(newOffsetX), Math.max(maxOffsetX, 0)) * Math.sign(newOffsetX));\n        setOffsetY(Math.min(Math.abs(newOffsetY), Math.max(maxOffsetY, 0)) * Math.sign(newOffsetY));\n    }, [zoom, offsetX, offsetY, slideRect, imageRect.width, imageRect.height]);\n    const changeZoom = React.useCallback((targetZoom, rapid, dx, dy) => {\n        const newZoom = round(Math.min(Math.max(targetZoom + 0.001 < maxZoom ? targetZoom : maxZoom, 1), maxZoom), 5);\n        if (newZoom === zoom)\n            return;\n        if (!rapid) {\n            animate();\n        }\n        changeOffsets(dx ? dx * (1 / zoom - 1 / newZoom) : 0, dy ? dy * (1 / zoom - 1 / newZoom) : 0, newZoom);\n        setZoom(newZoom);\n    }, [zoom, maxZoom, changeOffsets, animate]);\n    const handleControllerRectChange = useEventCallback(() => {\n        if (zoom > 1) {\n            if (zoom > maxZoom) {\n                changeZoom(maxZoom, true);\n            }\n            changeOffsets();\n        }\n    });\n    useLayoutEffect(handleControllerRectChange, [containerRect.width, containerRect.height, handleControllerRectChange]);\n    const zoomIn = React.useCallback(() => changeZoom(zoom * zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    const zoomOut = React.useCallback(() => changeZoom(zoom / zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    return { zoom, offsetX, offsetY, disabled, changeOffsets, changeZoom, zoomIn, zoomOut };\n}\n\nconst ZoomControllerContext = React.createContext(null);\nconst useZoom = makeUseContext(\"useZoom\", \"ZoomControllerContext\", ZoomControllerContext);\nfunction ZoomContextProvider({ children }) {\n    const [zoomWrapper, setZoomWrapper] = React.useState();\n    const { slideRect } = useController();\n    const { imageRect, maxZoom } = useZoomImageRect(slideRect, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.imageDimensions);\n    const { zoom, offsetX, offsetY, disabled, changeZoom, changeOffsets, zoomIn, zoomOut } = useZoomState(imageRect, maxZoom, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    useZoomCallback(zoom, disabled);\n    useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    const zoomRef = React.useMemo(() => ({ zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom }), [zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom]);\n    React.useImperativeHandle(useZoomProps().ref, () => zoomRef, [zoomRef]);\n    const context = React.useMemo(() => ({ ...zoomRef, setZoomWrapper }), [zoomRef, setZoomWrapper]);\n    return React.createElement(ZoomControllerContext.Provider, { value: context }, children);\n}\n\nconst ZoomInIcon = createIcon(\"ZoomIn\", React.createElement(React.Fragment, null,\n    React.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\" }),\n    React.createElement(\"path\", { d: \"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z\" })));\nconst ZoomOutIcon = createIcon(\"ZoomOut\", React.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z\" }));\nconst ZoomButton = React.forwardRef(function ZoomButton({ zoomIn, onLoseFocus }, ref) {\n    const wasEnabled = React.useRef(false);\n    const wasFocused = React.useRef(false);\n    const { zoom, maxZoom, zoomIn: zoomInCallback, zoomOut: zoomOutCallback, disabled: zoomDisabled } = useZoom();\n    const { render } = useLightboxProps();\n    const disabled = zoomDisabled || (zoomIn ? zoom >= maxZoom : zoom <= 1);\n    React.useEffect(() => {\n        if (disabled && wasEnabled.current && wasFocused.current) {\n            onLoseFocus();\n        }\n        if (!disabled) {\n            wasEnabled.current = true;\n        }\n    }, [disabled, onLoseFocus]);\n    return (React.createElement(IconButton, { ref: ref, disabled: disabled, label: zoomIn ? \"Zoom in\" : \"Zoom out\", icon: zoomIn ? ZoomInIcon : ZoomOutIcon, renderIcon: zoomIn ? render.iconZoomIn : render.iconZoomOut, onClick: zoomIn ? zoomInCallback : zoomOutCallback, onFocus: () => {\n            wasFocused.current = true;\n        }, onBlur: () => {\n            wasFocused.current = false;\n        } }));\n});\n\nfunction ZoomButtonsGroup() {\n    const zoomInRef = React.useRef(null);\n    const zoomOutRef = React.useRef(null);\n    const { focus } = useController();\n    const focusSibling = React.useCallback((sibling) => {\n        var _a, _b;\n        if (!((_a = sibling.current) === null || _a === void 0 ? void 0 : _a.disabled)) {\n            (_b = sibling.current) === null || _b === void 0 ? void 0 : _b.focus();\n        }\n        else {\n            focus();\n        }\n    }, [focus]);\n    const focusZoomIn = React.useCallback(() => focusSibling(zoomInRef), [focusSibling]);\n    const focusZoomOut = React.useCallback(() => focusSibling(zoomOutRef), [focusSibling]);\n    return (React.createElement(React.Fragment, null,\n        React.createElement(ZoomButton, { zoomIn: true, ref: zoomInRef, onLoseFocus: focusZoomOut }),\n        React.createElement(ZoomButton, { ref: zoomOutRef, onLoseFocus: focusZoomIn })));\n}\n\nfunction ZoomToolbarControl() {\n    const { render } = useLightboxProps();\n    const zoomRef = useZoom();\n    if (render.buttonZoom) {\n        return React.createElement(React.Fragment, null, render.buttonZoom(zoomRef));\n    }\n    return React.createElement(ZoomButtonsGroup, null);\n}\n\nfunction isResponsiveImageSlide(slide) {\n    var _a;\n    return (((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.length) || 0) > 0;\n}\nfunction reducer({ current, preload }, { type, source }) {\n    switch (type) {\n        case \"fetch\":\n            if (!current) {\n                return { current: source };\n            }\n            return { current, preload: source };\n        case \"done\":\n            if (source === preload) {\n                return { current: source };\n            }\n            return { current, preload };\n        default:\n            throw new Error(UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction ResponsiveImage(props) {\n    var _a, _b;\n    const [{ current, preload }, dispatch] = React.useReducer(reducer, {});\n    const { slide: image, rect, imageFit, render, interactive } = props;\n    const srcSet = image.srcSet.sort((a, b) => a.width - b.width);\n    const width = (_a = image.width) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1].width;\n    const height = (_b = image.height) !== null && _b !== void 0 ? _b : srcSet[srcSet.length - 1].height;\n    const cover = isImageFitCover(image, imageFit);\n    const maxWidth = Math.max(...srcSet.map((x) => x.width));\n    const targetWidth = Math.min((cover ? Math.max : Math.min)(rect.width, width * (rect.height / height)), maxWidth);\n    const pixelDensity = devicePixelRatio();\n    const handleResize = useEventCallback(() => {\n        var _a;\n        const targetSource = (_a = srcSet.find((x) => x.width >= targetWidth * pixelDensity)) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1];\n        if (!current || srcSet.findIndex((x) => x.src === current) < srcSet.findIndex((x) => x === targetSource)) {\n            dispatch({ type: \"fetch\", source: targetSource.src });\n        }\n    });\n    useLayoutEffect(handleResize, [rect.width, rect.height, pixelDensity, handleResize]);\n    const handlePreload = useEventCallback((currentPreload) => dispatch({ type: \"done\", source: currentPreload }));\n    const style = {\n        WebkitTransform: !interactive ? \"translateZ(0)\" : \"initial\",\n    };\n    if (!cover) {\n        Object.assign(style, rect.width / rect.height < width / height ? { width: \"100%\", height: \"auto\" } : { width: \"auto\", height: \"100%\" });\n    }\n    return (React.createElement(React.Fragment, null,\n        preload && preload !== current && (React.createElement(ImageSlide, { key: \"preload\", ...props, offset: undefined, slide: { ...image, src: preload, srcSet: undefined }, style: { position: \"absolute\", visibility: \"hidden\", ...style }, onLoad: () => handlePreload(preload), render: {\n                ...render,\n                iconLoading: () => null,\n                iconError: () => null,\n            } })),\n        current && (React.createElement(ImageSlide, { key: \"current\", ...props, slide: { ...image, src: current, srcSet: undefined }, style: style }))));\n}\n\nfunction ZoomWrapper({ render, slide, offset, rect }) {\n    var _a;\n    const [imageDimensions, setImageDimensions] = React.useState();\n    const zoomWrapperRef = React.useRef(null);\n    const { zoom, maxZoom, offsetX, offsetY, setZoomWrapper } = useZoom();\n    const interactive = zoom > 1;\n    const { carousel, on } = useLightboxProps();\n    const { currentIndex } = useLightboxState();\n    useLayoutEffect(() => {\n        if (offset === 0) {\n            setZoomWrapper({ zoomWrapperRef, imageDimensions });\n            return () => setZoomWrapper(undefined);\n        }\n        return () => { };\n    }, [offset, imageDimensions, setZoomWrapper]);\n    let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, { slide, offset, rect, zoom, maxZoom });\n    if (!rendered && isImageSlide(slide)) {\n        const slideProps = {\n            slide,\n            offset,\n            rect,\n            render,\n            imageFit: carousel.imageFit,\n            imageProps: carousel.imageProps,\n            onClick: offset === 0 ? () => { var _a; return (_a = on.click) === null || _a === void 0 ? void 0 : _a.call(on, { index: currentIndex }); } : undefined,\n        };\n        rendered = isResponsiveImageSlide(slide) ? (React.createElement(ResponsiveImage, { ...slideProps, slide: slide, interactive: interactive, rect: offset === 0 ? { width: rect.width * zoom, height: rect.height * zoom } : rect })) : (React.createElement(ImageSlide, { onLoad: (img) => setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight }), ...slideProps }));\n    }\n    if (!rendered)\n        return null;\n    return (React.createElement(\"div\", { ref: zoomWrapperRef, className: clsx(cssClass(CLASS_FULLSIZE), cssClass(CLASS_FLEX_CENTER), cssClass(CLASS_SLIDE_WRAPPER), interactive && cssClass(CLASS_SLIDE_WRAPPER_INTERACTIVE)), style: offset === 0 ? { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` } : undefined }, rendered));\n}\n\nconst Zoom = ({ augment, addModule }) => {\n    augment(({ zoom: zoomProps, toolbar, render, controller, ...restProps }) => {\n        const zoom = resolveZoomProps(zoomProps);\n        return {\n            zoom,\n            toolbar: addToolbarButton(toolbar, PLUGIN_ZOOM, React.createElement(ZoomToolbarControl, null)),\n            render: {\n                ...render,\n                slide: (props) => { var _a; return isImageSlide(props.slide) ? React.createElement(ZoomWrapper, { render: render, ...props }) : (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, props); },\n            },\n            controller: { ...controller, preventDefaultWheelY: zoom.scrollToZoom },\n            ...restProps,\n        };\n    });\n    addModule(createModule(PLUGIN_ZOOM, ZoomContextProvider));\n};\n\nexport { Zoom as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAIvB,IAAM,mBAAmB;AAAA,EACrB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,cAAc;AAClB;AACA,IAAM,mBAAmB,CAAC,UAAU;AAAA,EAChC,GAAG;AAAA,EACH,GAAG;AACP;AAEA,SAAS,iBAAiB,MAAM,SAAS,SAAS,gBAAgB;AAC9D,QAAM,gBAAsB,aAAO,MAAS;AAC5C,QAAM,qBAA2B,aAAO,MAAS;AACjD,QAAM,EAAE,MAAM,sBAAsB,IAAI,iBAAiB,EAAE;AAC3D,QAAM,eAAe,oBAAoB;AACzC,QAAM,oBAAoB,iBAAiB,MAAM;AAC7C,QAAI,IAAI,IAAI;AACZ,KAAC,KAAK,cAAc,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAC5E,kBAAc,UAAU;AACxB,QAAI,mBAAmB,YAAY,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,UAAU;AACxH,UAAI;AACA,sBAAc,WAAW,MAAM,KAAK,eAAe,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,UAClH,EAAE,WAAW,mBAAmB,QAAQ;AAAA,UACxC,EAAE,WAAW,SAAS,IAAI,gBAAgB,OAAO,kBAAkB,OAAO,MAAM;AAAA,QACpF,GAAG;AAAA,UACC,UAAU,CAAC,eAAgB,0BAA0B,QAAQ,0BAA0B,SAAS,wBAAwB,MAAO;AAAA,UAC/H,QAAQ,cAAc,UAAU,aAAa;AAAA,QACjD,CAAC;AAAA,MACL,SACO,KAAK;AACR,gBAAQ,MAAM,GAAG;AAAA,MACrB;AACA,yBAAmB,UAAU;AAC7B,UAAI,cAAc,SAAS;AACvB,sBAAc,QAAQ,WAAW,MAAM;AACnC,wBAAc,UAAU;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,kBAAgB,mBAAmB,CAAC,MAAM,SAAS,SAAS,iBAAiB,CAAC;AAC9E,SAAa,kBAAY,MAAM;AAC3B,uBAAmB,WAAW,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,WACvG,OAAO,iBAAiB,eAAe,OAAO,EAAE,YAChD;AAAA,EACV,GAAG,CAAC,cAAc,CAAC;AACvB;AAEA,SAAS,gBAAgB,MAAM,UAAU;AACrC,QAAM,EAAE,GAAG,IAAI,iBAAiB;AAChC,QAAM,iBAAiB,iBAAiB,MAAM;AAC1C,QAAI;AACJ,QAAI,CAAC,UAAU;AACX,OAAC,KAAK,GAAG,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,EAAE,KAAK,CAAC;AAAA,IAC5E;AAAA,EACJ,CAAC;AACD,EAAM,gBAAU,gBAAgB,CAAC,MAAM,cAAc,CAAC;AAC1D;AAEA,SAAS,eAAe;AACpB,QAAM,EAAE,KAAK,IAAI,iBAAiB;AAClC,SAAO,iBAAiB,IAAI;AAChC;AAEA,SAAS,iBAAiB,WAAW,iBAAiB;AAClD,MAAI,IAAI;AACR,MAAI,YAAY,EAAE,OAAO,GAAG,QAAQ,EAAE;AACtC,MAAI,eAAe,EAAE,OAAO,GAAG,QAAQ,EAAE;AACzC,QAAM,EAAE,aAAa,IAAI,iBAAiB;AAC1C,QAAM,EAAE,SAAS,IAAI,iBAAiB,EAAE;AACxC,QAAM,EAAE,kBAAkB,IAAI,aAAa;AAC3C,MAAI,aAAa,cAAc;AAC3B,UAAM,QAAQ,EAAE,GAAG,cAAc,GAAG,gBAAgB;AACpD,QAAI,aAAa,KAAK,GAAG;AACrB,YAAM,QAAQ,gBAAgB,OAAO,QAAQ;AAC7C,YAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,GAAG,OAAO,MAAM,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC;AAC5J,YAAM,SAAS,KAAK,IAAI,MAAM,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,GAAG,OAAO,MAAM,SAAS,CAAC,MAAM,MAAM,IAAI,CAAC,CAAC,CAAC;AAChK,UAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,QAAQ,KAAK,UAAU,SAAS,GAAG;AACxE,uBAAe,QACT;AAAA,UACE,OAAO,KAAK,MAAM,KAAK,IAAI,OAAQ,UAAU,QAAQ,UAAU,SAAU,MAAM,CAAC;AAAA,UAChF,QAAQ,KAAK,MAAM,KAAK,IAAI,QAAS,UAAU,SAAS,UAAU,QAAS,KAAK,CAAC;AAAA,QACrF,IACE,EAAE,OAAO,OAAO;AACtB,uBAAe;AAAA,UACX,OAAO,aAAa,QAAQ;AAAA,UAC5B,QAAQ,aAAa,SAAS;AAAA,QAClC;AACA,oBAAY,QACN;AAAA,UACE,OAAO,KAAK,IAAI,UAAU,OAAO,aAAa,OAAO,KAAK;AAAA,UAC1D,QAAQ,KAAK,IAAI,UAAU,QAAQ,aAAa,QAAQ,MAAM;AAAA,QAClE,IACE;AAAA,UACE,OAAO,KAAK,MAAM,KAAK,IAAI,UAAU,OAAQ,UAAU,SAAS,SAAU,OAAO,KAAK,CAAC;AAAA,UACvF,QAAQ,KAAK,MAAM,KAAK,IAAI,UAAU,QAAS,UAAU,QAAQ,QAAS,QAAQ,MAAM,CAAC;AAAA,QAC7F;AAAA,MACR;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,UAAU,UAAU,QAAQ,KAAK,IAAI,MAAM,aAAa,QAAQ,UAAU,OAAO,CAAC,GAAG,CAAC,IAAI;AAChG,SAAO,EAAE,WAAW,QAAQ;AAChC;AAEA,SAAS,SAAS,UAAU,UAAU;AAClC,WAAS,SAAS,UAAU,SAAS,YAAY,KAAK,SAAS,UAAU,SAAS,YAAY,MAAM;AACxG;AACA,SAAS,UAAU,OAAO,OAAO,SAAS,KAAK,QAAQ,GAAG;AACtD,SAAO,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,MAAM,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;AACnF;AACA,SAAS,eAAe,MAAM,SAAS,UAAU,YAAY,eAAe,gBAAgB;AACxF,QAAM,iBAAuB,aAAO,CAAC,CAAC;AACtC,QAAM,kBAAwB,aAAO,CAAC;AACtC,QAAM,oBAA0B,aAAO,MAAS;AAChD,QAAM,EAAE,YAAY,IAAI,iBAAiB;AACzC,QAAM,EAAE,eAAe,IAAI,mBAAmB;AAC9C,QAAM,EAAE,cAAc,iBAAiB,IAAI,cAAc;AACzD,QAAM,EAAE,sBAAsB,kBAAkB,yBAAyB,cAAc,gBAAgB,kBAAkB,qBAAqB,wBAAyB,IAAI,aAAa;AACxL,QAAM,uBAA6B,kBAAY,CAAC,UAAU;AACtD,QAAI,aAAa,SAAS;AACtB,YAAM,EAAE,OAAO,MAAM,IAAI;AACzB,YAAM,EAAE,SAAS,QAAQ,IAAI,eAAe;AAC5C,YAAM,EAAE,MAAM,KAAK,OAAO,OAAO,IAAI,aAAa,QAAQ,sBAAsB;AAChF,aAAO,CAAC,QAAQ,OAAO,UAAU,QAAQ,GAAG,QAAQ,MAAM,UAAU,SAAS,CAAC;AAAA,IAClF;AACA,WAAO,CAAC;AAAA,EACZ,GAAG,CAAC,cAAc,cAAc,CAAC;AACjC,QAAM,YAAY,iBAAiB,CAAC,UAAU;AAC1C,UAAM,EAAE,KAAK,SAAS,QAAQ,IAAI;AAClC,UAAM,OAAO,WAAW;AACxB,UAAM,iBAAiB,MAAM;AACzB,YAAM,eAAe;AACrB,YAAM,gBAAgB;AAAA,IAC1B;AACA,QAAI,OAAO,GAAG;AACV,YAAM,OAAO,CAAC,QAAQ,WAAW;AAC7B,uBAAe;AACf,sBAAc,QAAQ,MAAM;AAAA,MAChC;AACA,UAAI,QAAQ,aAAa;AACrB,aAAK,GAAG,oBAAoB;AAAA,MAChC,WACS,QAAQ,WAAW;AACxB,aAAK,GAAG,CAAC,oBAAoB;AAAA,MACjC,WACS,QAAQ,aAAa;AAC1B,aAAK,CAAC,sBAAsB,CAAC;AAAA,MACjC,WACS,QAAQ,cAAc;AAC3B,aAAK,sBAAsB,CAAC;AAAA,MAChC;AAAA,IACJ;AACA,UAAM,mBAAmB,CAAC,cAAc;AACpC,qBAAe;AACf,iBAAW,SAAS;AAAA,IACxB;AACA,QAAI,QAAQ,OAAQ,QAAQ,QAAQ,KAAM;AACtC,uBAAiB,OAAO,gBAAgB;AAAA,IAC5C,WACS,QAAQ,OAAQ,QAAQ,QAAQ,KAAM;AAC3C,uBAAiB,OAAO,gBAAgB;AAAA,IAC5C,WACS,QAAQ,QAAQ,KAAK;AAC1B,uBAAiB,CAAC;AAAA,IACtB;AAAA,EACJ,CAAC;AACD,QAAM,UAAU,iBAAiB,CAAC,UAAU;AACxC,QAAI,MAAM,WAAW,cAAc;AAC/B,UAAI,KAAK,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,MAAM,MAAM,GAAG;AACjD,cAAM,gBAAgB;AACtB,mBAAW,UAAU,MAAM,CAAC,MAAM,QAAQ,uBAAuB,GAAG,MAAM,GAAG,qBAAqB,KAAK,CAAC;AACxG;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,GAAG;AACV,YAAM,gBAAgB;AACtB,UAAI,CAAC,cAAc;AACf,sBAAc,MAAM,QAAQ,MAAM,MAAM;AAAA,MAC5C;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,eAAqB,kBAAY,CAAC,UAAU;AAC9C,UAAM,WAAW,eAAe;AAChC,aAAS,OAAO,GAAG,SAAS,QAAQ,GAAG,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,MAAM,SAAS,CAAC;AAAA,EAClG,GAAG,CAAC,CAAC;AACL,QAAM,iBAAuB,kBAAY,CAAC,UAAU;AAChD,iBAAa,KAAK;AAClB,UAAM,QAAQ;AACd,mBAAe,QAAQ,KAAK,KAAK;AAAA,EACrC,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,gBAAgB,iBAAiB,CAAC,UAAU;AAC9C,QAAI;AACJ,UAAM,WAAW,eAAe;AAChC,QAAK,MAAM,gBAAgB,WAAW,MAAM,UAAU,KAClD,GAAG,KAAK,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,MAAM,MAAM,IAAI;AACjK;AAAA,IACJ;AACA,QAAI,OAAO,GAAG;AACV,YAAM,gBAAgB;AAAA,IAC1B;AACA,UAAM,EAAE,UAAU,IAAI;AACtB,QAAI,SAAS,WAAW,KACpB,YAAY,gBAAgB,WAAW,MAAM,gBAAgB,UAAU,iBAAiB,mBAAmB;AAC3G,sBAAgB,UAAU;AAC1B,iBAAW,SAAS,UAAU,OAAO,KAAK,IAAI,YAAY,IAAI,sBAAsB,gBAAgB,IAAI,GAAG,OAAO,GAAG,qBAAqB,KAAK,CAAC;AAAA,IACpJ,OACK;AACD,sBAAgB,UAAU;AAAA,IAC9B;AACA,mBAAe,KAAK;AACpB,QAAI,SAAS,WAAW,GAAG;AACvB,wBAAkB,UAAU,SAAS,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,IACjE;AAAA,EACJ,CAAC;AACD,QAAM,gBAAgB,iBAAiB,CAAC,UAAU;AAC9C,UAAM,WAAW,eAAe;AAChC,UAAM,gBAAgB,SAAS,KAAK,CAAC,MAAM,EAAE,cAAc,MAAM,SAAS;AAC1E,QAAI,SAAS,WAAW,KAAK,kBAAkB,SAAS;AACpD,YAAM,gBAAgB;AACtB,qBAAe,KAAK;AACpB,YAAM,kBAAkB,SAAS,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AACzD,YAAM,QAAQ,kBAAkB,kBAAkB;AAClD,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AACrB,mBAAW,UAAU,MAAM,OAAO,uBAAuB,GAAG,MAAM,GAAG,SAChE,IAAI,CAAC,MAAM,qBAAqB,CAAC,CAAC,EAClC,OAAO,CAAC,KAAK,eAAe,WAAW,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;AAC1E,0BAAkB,UAAU;AAAA,MAChC;AACA;AAAA,IACJ;AACA,QAAI,OAAO,GAAG;AACV,YAAM,gBAAgB;AACtB,UAAI,eAAe;AACf,YAAI,SAAS,WAAW,GAAG;AACvB,yBAAe,cAAc,UAAU,MAAM,WAAW,OAAO,cAAc,UAAU,MAAM,WAAW,IAAI;AAAA,QAChH;AACA,uBAAe,KAAK;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,cAAoB,kBAAY,CAAC,UAAU;AAC7C,UAAM,WAAW,eAAe;AAChC,QAAI,SAAS,WAAW,KAAK,SAAS,KAAK,CAAC,MAAM,EAAE,cAAc,MAAM,SAAS,GAAG;AAChF,wBAAkB,UAAU;AAAA,IAChC;AACA,iBAAa,KAAK;AAAA,EACtB,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,iBAAuB,kBAAY,MAAM;AAC3C,UAAM,WAAW,eAAe;AAChC,aAAS,OAAO,GAAG,SAAS,MAAM;AAClC,oBAAgB,UAAU;AAC1B,sBAAkB,UAAU;AAAA,EAChC,GAAG,CAAC,CAAC;AACL,mBAAiB,kBAAkB,eAAe,eAAe,aAAa,QAAQ;AACtF,EAAM,gBAAU,gBAAgB,CAAC,aAAa,cAAc,CAAC;AAC7D,EAAM,gBAAU,MAAM;AAClB,QAAI,CAAC,UAAU;AACX,aAAO,QAAQ,gBAAgB,iBAAiB,mBAAmB,SAAS,GAAG,iBAAiB,gBAAgB,OAAO,CAAC;AAAA,IAC5H;AACA,WAAO,MAAM;AAAA,IAAE;AAAA,EACnB,GAAG,CAAC,UAAU,kBAAkB,gBAAgB,WAAW,OAAO,CAAC;AACvE;AAEA,SAAS,aAAa,WAAW,SAAS,gBAAgB;AACtD,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS,CAAC;AACxC,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,CAAC;AAC9C,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,CAAC;AAC9C,QAAM,UAAU,iBAAiB,MAAM,SAAS,SAAS,cAAc;AACvE,QAAM,EAAE,cAAc,YAAY,IAAI,iBAAiB;AACvD,QAAM,EAAE,eAAe,UAAU,IAAI,cAAc;AACnD,QAAM,EAAE,iBAAiB,IAAI,aAAa;AAC1C,QAAM,gBAAgB,gBAAgB,aAAa,YAAY,IAAI,aAAa,MAAM;AACtF,QAAM,WAAW,CAAC,iBAAiB,EAAE,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe;AACpH,kBAAgB,MAAM;AAClB,YAAQ,CAAC;AACT,eAAW,CAAC;AACZ,eAAW,CAAC;AAAA,EAChB,GAAG,CAAC,aAAa,aAAa,CAAC;AAC/B,QAAM,gBAAsB,kBAAY,CAAC,IAAI,IAAI,eAAe;AAC5D,UAAM,UAAU,cAAc;AAC9B,UAAM,aAAa,WAAW,MAAM;AACpC,UAAM,aAAa,WAAW,MAAM;AACpC,UAAM,cAAc,UAAU,QAAQ,UAAU,UAAU,SAAS,IAAI;AACvE,UAAM,cAAc,UAAU,SAAS,UAAU,UAAU,UAAU,IAAI;AACzE,eAAW,KAAK,IAAI,KAAK,IAAI,UAAU,GAAG,KAAK,IAAI,YAAY,CAAC,CAAC,IAAI,KAAK,KAAK,UAAU,CAAC;AAC1F,eAAW,KAAK,IAAI,KAAK,IAAI,UAAU,GAAG,KAAK,IAAI,YAAY,CAAC,CAAC,IAAI,KAAK,KAAK,UAAU,CAAC;AAAA,EAC9F,GAAG,CAAC,MAAM,SAAS,SAAS,WAAW,UAAU,OAAO,UAAU,MAAM,CAAC;AACzE,QAAM,aAAmB,kBAAY,CAAC,YAAY,OAAO,IAAI,OAAO;AAChE,UAAM,UAAU,MAAM,KAAK,IAAI,KAAK,IAAI,aAAa,OAAQ,UAAU,aAAa,SAAS,CAAC,GAAG,OAAO,GAAG,CAAC;AAC5G,QAAI,YAAY;AACZ;AACJ,QAAI,CAAC,OAAO;AACR,cAAQ;AAAA,IACZ;AACA,kBAAc,KAAK,MAAM,IAAI,OAAO,IAAI,WAAW,GAAG,KAAK,MAAM,IAAI,OAAO,IAAI,WAAW,GAAG,OAAO;AACrG,YAAQ,OAAO;AAAA,EACnB,GAAG,CAAC,MAAM,SAAS,eAAe,OAAO,CAAC;AAC1C,QAAM,6BAA6B,iBAAiB,MAAM;AACtD,QAAI,OAAO,GAAG;AACV,UAAI,OAAO,SAAS;AAChB,mBAAW,SAAS,IAAI;AAAA,MAC5B;AACA,oBAAc;AAAA,IAClB;AAAA,EACJ,CAAC;AACD,kBAAgB,4BAA4B,CAAC,cAAc,OAAO,cAAc,QAAQ,0BAA0B,CAAC;AACnH,QAAM,SAAe,kBAAY,MAAM,WAAW,OAAO,gBAAgB,GAAG,CAAC,MAAM,kBAAkB,UAAU,CAAC;AAChH,QAAM,UAAgB,kBAAY,MAAM,WAAW,OAAO,gBAAgB,GAAG,CAAC,MAAM,kBAAkB,UAAU,CAAC;AACjH,SAAO,EAAE,MAAM,SAAS,SAAS,UAAU,eAAe,YAAY,QAAQ,QAAQ;AAC1F;AAEA,IAAM,wBAA8B,oBAAc,IAAI;AACtD,IAAM,UAAU,eAAe,WAAW,yBAAyB,qBAAqB;AACxF,SAAS,oBAAoB,EAAE,SAAS,GAAG;AACvC,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS;AACrD,QAAM,EAAE,UAAU,IAAI,cAAc;AACpC,QAAM,EAAE,WAAW,QAAQ,IAAI,iBAAiB,WAAW,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,eAAe;AAChJ,QAAM,EAAE,MAAM,SAAS,SAAS,UAAU,YAAY,eAAe,QAAQ,QAAQ,IAAI,aAAa,WAAW,SAAS,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,cAAc;AAC9M,kBAAgB,MAAM,QAAQ;AAC9B,iBAAe,MAAM,SAAS,UAAU,YAAY,eAAe,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,cAAc;AACvJ,QAAM,UAAgB,cAAQ,OAAO,EAAE,MAAM,SAAS,SAAS,SAAS,UAAU,QAAQ,SAAS,WAAW,IAAI,CAAC,MAAM,SAAS,SAAS,SAAS,UAAU,QAAQ,SAAS,UAAU,CAAC;AAC1L,EAAM,0BAAoB,aAAa,EAAE,KAAK,MAAM,SAAS,CAAC,OAAO,CAAC;AACtE,QAAM,UAAgB,cAAQ,OAAO,EAAE,GAAG,SAAS,eAAe,IAAI,CAAC,SAAS,cAAc,CAAC;AAC/F,SAAa,oBAAc,sBAAsB,UAAU,EAAE,OAAO,QAAQ,GAAG,QAAQ;AAC3F;AAEA,IAAM,aAAa,WAAW,UAAgB;AAAA,EAAoB;AAAA,EAAU;AAAA,EAClE,oBAAc,QAAQ,EAAE,GAAG,6OAA6O,CAAC;AAAA,EACzQ,oBAAc,QAAQ,EAAE,GAAG,oCAAoC,CAAC;AAAC,CAAC;AAC5E,IAAM,cAAc,WAAW,WAAiB,oBAAc,QAAQ,EAAE,GAAG,wPAAwP,CAAC,CAAC;AACrU,IAAM,aAAmB,iBAAW,SAASA,YAAW,EAAE,QAAQ,YAAY,GAAG,KAAK;AAClF,QAAM,aAAmB,aAAO,KAAK;AACrC,QAAM,aAAmB,aAAO,KAAK;AACrC,QAAM,EAAE,MAAM,SAAS,QAAQ,gBAAgB,SAAS,iBAAiB,UAAU,aAAa,IAAI,QAAQ;AAC5G,QAAM,EAAE,OAAO,IAAI,iBAAiB;AACpC,QAAM,WAAW,iBAAiB,SAAS,QAAQ,UAAU,QAAQ;AACrE,EAAM,gBAAU,MAAM;AAClB,QAAI,YAAY,WAAW,WAAW,WAAW,SAAS;AACtD,kBAAY;AAAA,IAChB;AACA,QAAI,CAAC,UAAU;AACX,iBAAW,UAAU;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,UAAU,WAAW,CAAC;AAC1B,SAAc,oBAAc,YAAY,EAAE,KAAU,UAAoB,OAAO,SAAS,YAAY,YAAY,MAAM,SAAS,aAAa,aAAa,YAAY,SAAS,OAAO,aAAa,OAAO,aAAa,SAAS,SAAS,iBAAiB,iBAAiB,SAAS,MAAM;AACjR,eAAW,UAAU;AAAA,EACzB,GAAG,QAAQ,MAAM;AACb,eAAW,UAAU;AAAA,EACzB,EAAE,CAAC;AACX,CAAC;AAED,SAAS,mBAAmB;AACxB,QAAM,YAAkB,aAAO,IAAI;AACnC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,EAAE,MAAM,IAAI,cAAc;AAChC,QAAM,eAAqB,kBAAY,CAAC,YAAY;AAChD,QAAI,IAAI;AACR,QAAI,GAAG,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAC5E,OAAC,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACzE,OACK;AACD,YAAM;AAAA,IACV;AAAA,EACJ,GAAG,CAAC,KAAK,CAAC;AACV,QAAM,cAAoB,kBAAY,MAAM,aAAa,SAAS,GAAG,CAAC,YAAY,CAAC;AACnF,QAAM,eAAqB,kBAAY,MAAM,aAAa,UAAU,GAAG,CAAC,YAAY,CAAC;AACrF,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IAClC,oBAAc,YAAY,EAAE,QAAQ,MAAM,KAAK,WAAW,aAAa,aAAa,CAAC;AAAA,IACrF,oBAAc,YAAY,EAAE,KAAK,YAAY,aAAa,YAAY,CAAC;AAAA,EAAC;AACtF;AAEA,SAAS,qBAAqB;AAC1B,QAAM,EAAE,OAAO,IAAI,iBAAiB;AACpC,QAAM,UAAU,QAAQ;AACxB,MAAI,OAAO,YAAY;AACnB,WAAa,oBAAoB,gBAAU,MAAM,OAAO,WAAW,OAAO,CAAC;AAAA,EAC/E;AACA,SAAa,oBAAc,kBAAkB,IAAI;AACrD;AAEA,SAAS,uBAAuB,OAAO;AACnC,MAAI;AACJ,YAAU,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,KAAK;AACzF;AACA,SAAS,QAAQ,EAAE,SAAS,QAAQ,GAAG,EAAE,MAAM,OAAO,GAAG;AACrD,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,UAAI,CAAC,SAAS;AACV,eAAO,EAAE,SAAS,OAAO;AAAA,MAC7B;AACA,aAAO,EAAE,SAAS,SAAS,OAAO;AAAA,IACtC,KAAK;AACD,UAAI,WAAW,SAAS;AACpB,eAAO,EAAE,SAAS,OAAO;AAAA,MAC7B;AACA,aAAO,EAAE,SAAS,QAAQ;AAAA,IAC9B;AACI,YAAM,IAAI,MAAM,mBAAmB;AAAA,EAC3C;AACJ;AACA,SAAS,gBAAgB,OAAO;AAC5B,MAAI,IAAI;AACR,QAAM,CAAC,EAAE,SAAS,QAAQ,GAAG,QAAQ,IAAU,iBAAW,SAAS,CAAC,CAAC;AACrE,QAAM,EAAE,OAAO,OAAO,MAAM,UAAU,QAAQ,YAAY,IAAI;AAC9D,QAAM,SAAS,MAAM,OAAO,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAC5D,QAAM,SAAS,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK,OAAO,OAAO,SAAS,CAAC,EAAE;AAC5F,QAAM,UAAU,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,OAAO,SAAS,CAAC,EAAE;AAC9F,QAAM,QAAQ,gBAAgB,OAAO,QAAQ;AAC7C,QAAM,WAAW,KAAK,IAAI,GAAG,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AACvD,QAAM,cAAc,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,SAAS,KAAK,SAAS,OAAO,GAAG,QAAQ;AAChH,QAAM,eAAe,iBAAiB;AACtC,QAAM,eAAe,iBAAiB,MAAM;AACxC,QAAIC;AACJ,UAAM,gBAAgBA,MAAK,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,cAAc,YAAY,OAAO,QAAQA,QAAO,SAASA,MAAK,OAAO,OAAO,SAAS,CAAC;AAC/I,QAAI,CAAC,WAAW,OAAO,UAAU,CAAC,MAAM,EAAE,QAAQ,OAAO,IAAI,OAAO,UAAU,CAAC,MAAM,MAAM,YAAY,GAAG;AACtG,eAAS,EAAE,MAAM,SAAS,QAAQ,aAAa,IAAI,CAAC;AAAA,IACxD;AAAA,EACJ,CAAC;AACD,kBAAgB,cAAc,CAAC,KAAK,OAAO,KAAK,QAAQ,cAAc,YAAY,CAAC;AACnF,QAAM,gBAAgB,iBAAiB,CAAC,mBAAmB,SAAS,EAAE,MAAM,QAAQ,QAAQ,eAAe,CAAC,CAAC;AAC7G,QAAM,QAAQ;AAAA,IACV,iBAAiB,CAAC,cAAc,kBAAkB;AAAA,EACtD;AACA,MAAI,CAAC,OAAO;AACR,WAAO,OAAO,OAAO,KAAK,QAAQ,KAAK,SAAS,QAAQ,SAAS,EAAE,OAAO,QAAQ,QAAQ,OAAO,IAAI,EAAE,OAAO,QAAQ,QAAQ,OAAO,CAAC;AAAA,EAC1I;AACA,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,WAAW,YAAY,WAAkB,oBAAc,YAAY,EAAE,KAAK,WAAW,GAAG,OAAO,QAAQ,QAAW,OAAO,EAAE,GAAG,OAAO,KAAK,SAAS,QAAQ,OAAU,GAAG,OAAO,EAAE,UAAU,YAAY,YAAY,UAAU,GAAG,MAAM,GAAG,QAAQ,MAAM,cAAc,OAAO,GAAG,QAAQ;AAAA,MAC/Q,GAAG;AAAA,MACH,aAAa,MAAM;AAAA,MACnB,WAAW,MAAM;AAAA,IACrB,EAAE,CAAC;AAAA,IACP,WAAkB,oBAAc,YAAY,EAAE,KAAK,WAAW,GAAG,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,SAAS,QAAQ,OAAU,GAAG,MAAa,CAAC;AAAA,EAAE;AACtJ;AAEA,SAAS,YAAY,EAAE,QAAQ,OAAO,QAAQ,KAAK,GAAG;AAClD,MAAI;AACJ,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,eAAS;AAC7D,QAAM,iBAAuB,aAAO,IAAI;AACxC,QAAM,EAAE,MAAM,SAAS,SAAS,SAAS,eAAe,IAAI,QAAQ;AACpE,QAAM,cAAc,OAAO;AAC3B,QAAM,EAAE,UAAU,GAAG,IAAI,iBAAiB;AAC1C,QAAM,EAAE,aAAa,IAAI,iBAAiB;AAC1C,kBAAgB,MAAM;AAClB,QAAI,WAAW,GAAG;AACd,qBAAe,EAAE,gBAAgB,gBAAgB,CAAC;AAClD,aAAO,MAAM,eAAe,MAAS;AAAA,IACzC;AACA,WAAO,MAAM;AAAA,IAAE;AAAA,EACnB,GAAG,CAAC,QAAQ,iBAAiB,cAAc,CAAC;AAC5C,MAAI,YAAY,KAAK,OAAO,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,EAAE,OAAO,QAAQ,MAAM,MAAM,QAAQ,CAAC;AAC9H,MAAI,CAAC,YAAY,aAAa,KAAK,GAAG;AAClC,UAAM,aAAa;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,SAAS;AAAA,MACnB,YAAY,SAAS;AAAA,MACrB,SAAS,WAAW,IAAI,MAAM;AAAE,YAAIA;AAAI,gBAAQA,MAAK,GAAG,WAAW,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,IAAI,EAAE,OAAO,aAAa,CAAC;AAAA,MAAG,IAAI;AAAA,IAClJ;AACA,eAAW,uBAAuB,KAAK,IAAW,oBAAc,iBAAiB,EAAE,GAAG,YAAY,OAAc,aAA0B,MAAM,WAAW,IAAI,EAAE,OAAO,KAAK,QAAQ,MAAM,QAAQ,KAAK,SAAS,KAAK,IAAI,KAAK,CAAC,IAAY,oBAAc,YAAY,EAAE,QAAQ,CAAC,QAAQ,mBAAmB,EAAE,OAAO,IAAI,cAAc,QAAQ,IAAI,cAAc,CAAC,GAAG,GAAG,WAAW,CAAC;AAAA,EACxX;AACA,MAAI,CAAC;AACD,WAAO;AACX,SAAc,oBAAc,OAAO,EAAE,KAAK,gBAAgB,WAAW,KAAK,SAAS,cAAc,GAAG,SAAS,iBAAiB,GAAG,SAAS,mBAAmB,GAAG,eAAe,SAAS,+BAA+B,CAAC,GAAG,OAAO,WAAW,IAAI,EAAE,WAAW,SAAS,IAAI,gBAAgB,OAAO,kBAAkB,OAAO,MAAM,IAAI,OAAU,GAAG,QAAQ;AAC9V;AAEA,IAAM,OAAO,CAAC,EAAE,SAAS,UAAU,MAAM;AACrC,UAAQ,CAAC,EAAE,MAAM,WAAW,SAAS,QAAQ,YAAY,GAAG,UAAU,MAAM;AACxE,UAAM,OAAO,iBAAiB,SAAS;AACvC,WAAO;AAAA,MACH;AAAA,MACA,SAAS,iBAAiB,SAAS,aAAmB,oBAAc,oBAAoB,IAAI,CAAC;AAAA,MAC7F,QAAQ;AAAA,QACJ,GAAG;AAAA,QACH,OAAO,CAAC,UAAU;AAAE,cAAI;AAAI,iBAAO,aAAa,MAAM,KAAK,IAAU,oBAAc,aAAa,EAAE,QAAgB,GAAG,MAAM,CAAC,KAAK,KAAK,OAAO,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,KAAK;AAAA,QAAG;AAAA,MACrN;AAAA,MACA,YAAY,EAAE,GAAG,YAAY,sBAAsB,KAAK,aAAa;AAAA,MACrE,GAAG;AAAA,IACP;AAAA,EACJ,CAAC;AACD,YAAU,aAAa,aAAa,mBAAmB,CAAC;AAC5D;", "names": ["ZoomButton", "_a"]}