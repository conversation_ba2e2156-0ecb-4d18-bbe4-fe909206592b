# Kế hoạch triển khai màn hình quản lý nhân viên

## Tổng quan
Màn hình quản lý nhân viên sẽ cho phép quản trị viên xem, thê<PERSON>, sửa và xóa thông tin nhân viên trong hệ thống. Màn hình này sẽ được phát triển dựa trên màn hình quản lý bất động sản hiện có.

## Các tính năng chính
1. **Khu vực bộ lọc**: L<PERSON><PERSON> nhân viên theo email, tên và số điện thoại
2. **Bảng dữ liệu**: Hiển thị danh sách nhân viên với phân trang và sắp xếp
3. **Thêm nhân viên**: <PERSON><PERSON><PERSON> thêm nhân viên mới thông qua modal popup
4. **Quản lý nhân viên**: <PERSON><PERSON><PERSON> tù<PERSON> chọ<PERSON> chỉnh sửa, xem chi tiết và xóa cho mỗi nhân viên
5. **Quản lý vai trò**: Lấy danh sách vai trò từ API và cho phép gán vai trò cho nhân viên

## Cấu trúc API
Các API endpoint sẽ được sử dụng:
- `GET /api/User/employees`: Lấy danh sách nhân viên với bộ lọc và phân trang
- `POST /api/User/employees`: Thêm nhân viên mới
- `GET /api/User/{id}`: Lấy thông tin chi tiết của nhân viên
- `PUT /api/User/{userId}/status`: Cập nhật trạng thái nhân viên (kích hoạt/vô hiệu hóa)
- `GET /api/RolePermission/roles`: Lấy danh sách vai trò
- `PUT /api/User/roles`: Cập nhật vai trò cho nhân viên

## Cấu trúc thư mục
Các tệp mới sẽ được tạo theo cấu trúc sau:

- **Types:** `src/lib/types/employee.ts` - Chứa các interface TypeScript cho Employee và các model liên quan
- **Features:**
  - `src/features/employee-management/` - Thư mục chính cho tính năng quản lý nhân viên
    - `components/` - Các components riêng cho tính năng nhân viên
      - `employee-table.tsx`: Bảng dữ liệu hiển thị danh sách nhân viên
      - `employee-filter.tsx`: Component bộ lọc tìm kiếm nhân viên
      - `employee-form.tsx`: Form thêm và chỉnh sửa nhân viên
      - `role-selector.tsx`: Component chọn vai trò cho nhân viên
    - `hooks/use-employee.ts` - Custom hooks cho tính năng nhân viên (nếu cần)
- **Services:** `src/services/employee-service.ts` - Các hàm để tương tác với API của nhân viên
- **Routes/Pages:**
  - `src/routes/_authenticated/employee/index.tsx`: Trang chính liệt kê danh sách nhân viên

## Giao diện người dùng

### 1. Khu vực bộ lọc
- Các trường tìm kiếm: Email, Tên, Số điện thoại
- Nút "Áp dụng bộ lọc" để gửi yêu cầu tìm kiếm
- Nút "Thêm nhân viên" để mở modal thêm nhân viên mới

### 2. Bảng dữ liệu nhân viên
- Các cột: Họ tên, Email, Số điện thoại, Loại người dùng, Trạng thái, Ngày tạo, Hành động
- Phân trang với hiển thị số trang và điều hướng
- Sắp xếp theo các cột
- Bảng sử dụng ShadcnUI table trong folder `src/components/ui/table.tsx`

### 3. Modal thêm nhân viên
- Form với các trường: Họ tên, Email, Mật khẩu, Số điện thoại
- Danh sách vai trò được lấy từ API để người dùng chọn
- Nút "Thêm" để lưu thông tin
- Nút "Thoát" để đóng modal

### 4. Modal chỉnh sửa/xem chi tiết nhân viên
- Hiển thị thông tin chi tiết của nhân viên
- Chế độ xem: Chỉ hiển thị thông tin
- Chế độ chỉnh sửa:
  - Cho phép thay đổi trạng thái (kích hoạt/vô hiệu hóa)
  - Cho phép thay đổi vai trò của nhân viên
- Nút "Lưu" (chỉ có ở chế độ chỉnh sửa)
- Nút "Đóng" để đóng modal

### 5. Modal xác nhận xóa
- Thông báo cảnh báo về việc xóa vĩnh viễn
- Nút "Xác nhận xóa" để thực hiện xóa
- Nút "Thoát" để hủy và đóng modal

## Luồng làm việc

### Hiển thị danh sách nhân viên
1. Tải dữ liệu nhân viên từ API với các tham số mặc định
2. Hiển thị dữ liệu trong bảng với phân trang

### Lọc danh sách nhân viên
1. Người dùng nhập các tiêu chí tìm kiếm
2. Khi nhấn "Áp dụng bộ lọc", gửi yêu cầu API với các tham số tìm kiếm
3. Cập nhật bảng dữ liệu với kết quả trả về

### Thêm nhân viên mới
1. Người dùng nhấn "Thêm nhân viên"
2. Lấy danh sách vai trò từ API `/api/RolePermission/roles`
3. Hiển thị modal với form thêm nhân viên và danh sách vai trò
4. Người dùng nhập thông tin, chọn vai trò và nhấn "Thêm"
5. Gửi yêu cầu API để tạo nhân viên mới
6. Nếu thành công, đóng modal và làm mới danh sách nhân viên
7. Nếu thất bại, hiển thị thông báo lỗi

### Chỉnh sửa/Xem chi tiết nhân viên
1. Người dùng nhấn vào tùy chọn "Chỉnh sửa" hoặc "Xem chi tiết" từ menu dropdown
2. Lấy thông tin chi tiết của nhân viên từ API
3. Lấy danh sách vai trò từ API `/api/RolePermission/roles`
4. Hiển thị modal với thông tin nhân viên
5. Trong chế độ chỉnh sửa:
   - Người dùng có thể thay đổi trạng thái nhân viên
   - Người dùng có thể thay đổi vai trò của nhân viên
   - Khi lưu, gửi yêu cầu API để cập nhật trạng thái và vai trò
6. Sau khi lưu thành công, đóng modal và làm mới danh sách

### Xóa nhân viên
1. Người dùng nhấn vào tùy chọn "Xóa" từ menu dropdown
2. Hiển thị modal xác nhận xóa
3. Nếu người dùng xác nhận, gửi yêu cầu API để xóa nhân viên
4. Sau khi xóa thành công, làm mới danh sách nhân viên

## Kế hoạch triển khai
1. Tạo file `src/lib/types/employee.ts` cho các định nghĩa types
2. Tạo thư mục `src/features/employee-management` và cấu trúc con
3. Tạo file `src/services/employee-service.ts` cho các services API
4. Triển khai các components cho tính năng:
   - Bảng danh sách nhân viên
   - Form tìm kiếm
   - Modal thêm nhân viên
      - Lấy danh sách vai trò
      - Cập nhật vai trò cho nhân viên
   - Modal chỉnh sửa/xem chi tiết
   - Modal xác nhận xóa
5. Tạo trang route trong `src/routes/_authenticated/employee/index.tsx`
6. Tích hợp với hệ thống định tuyến và quản lý trạng thái
7. Kiểm tra và tối ưu hóa hiệu suất

## Lưu ý
- Đảm bảo xác thực và phân quyền phù hợp
- Xử lý lỗi và hiển thị thông báo phù hợp
- Đảm bảo giao diện người dùng nhất quán với phần còn lại của ứng dụng 