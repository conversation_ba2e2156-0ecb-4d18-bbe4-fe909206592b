import {
  require_jsx_runtime
} from "./chunk-LYJUZW3I.js";
import {
  require_react
} from "./chunk-TJE776R7.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/react-photo-album/dist/client/aggregate.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react6 = __toESM(require_react(), 1);

// node_modules/react-photo-album/dist/client/rows.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);

// node_modules/react-photo-album/dist/client/hooks.js
var import_react = __toESM(require_react(), 1);
function useArray(array) {
  const ref = (0, import_react.useRef)(array);
  if (!array || !ref.current || array.length !== ref.current.length || ref.current.some((el, i) => el !== array[i])) {
    ref.current = array;
  }
  return ref.current;
}
function containerWidthReducer(state, [newContainerWidth, newScrollbarWidth]) {
  const [containerWidth, scrollbarWidth] = state;
  if (containerWidth !== void 0 && scrollbarWidth !== void 0 && newContainerWidth !== void 0 && newScrollbarWidth !== void 0 && newContainerWidth > containerWidth && newContainerWidth - containerWidth <= 20 && newScrollbarWidth < scrollbarWidth) {
    return [containerWidth, newScrollbarWidth];
  }
  return containerWidth !== newContainerWidth || scrollbarWidth !== newScrollbarWidth ? [newContainerWidth, newScrollbarWidth] : state;
}
function resolveContainerWidth(el, breakpoints2) {
  let width = el == null ? void 0 : el.clientWidth;
  if (width !== void 0 && breakpoints2 && breakpoints2.length > 0) {
    const sorted = [...breakpoints2.filter((x) => x > 0)].sort((a, b) => b - a);
    sorted.push(Math.floor(sorted[sorted.length - 1] / 2));
    width = sorted.find((breakpoint, index) => breakpoint <= width || index === sorted.length - 1);
  }
  return width;
}
function useContainerWidth(ref, breakpointsArray, defaultContainerWidth) {
  const [[containerWidth], dispatch] = (0, import_react.useReducer)(containerWidthReducer, [defaultContainerWidth]);
  const breakpoints2 = useArray(breakpointsArray);
  const observerRef = (0, import_react.useRef)(void 0);
  const containerRef = (0, import_react.useCallback)(
    (node) => {
      var _a;
      (_a = observerRef.current) == null ? void 0 : _a.disconnect();
      observerRef.current = void 0;
      const updateWidth = () => dispatch([resolveContainerWidth(node, breakpoints2), window.innerWidth - document.documentElement.clientWidth]);
      updateWidth();
      if (node && typeof ResizeObserver !== "undefined") {
        observerRef.current = new ResizeObserver(updateWidth);
        observerRef.current.observe(node);
      }
      if (typeof ref === "function") {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    },
    [ref, breakpoints2]
  );
  return { containerRef, containerWidth };
}

// node_modules/react-photo-album/dist/static/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);

// node_modules/react-photo-album/dist/utils/index.js
function clsx(...classes) {
  return [...classes].filter(Boolean).join(" ");
}
function cssClass(suffix) {
  return ["react-photo-album", suffix].filter(Boolean).join("--");
}
function cssVar(suffix) {
  return `--${cssClass(suffix)}`;
}
function ratio({ width, height }) {
  return width / height;
}
var breakpoints = Object.freeze([1200, 600, 300, 0]);
function unwrap(value, arg) {
  return typeof value === "function" ? value(arg) : value;
}
function unwrapParameter(value, containerWidth) {
  return containerWidth !== void 0 ? unwrap(value, containerWidth) : void 0;
}
function selectResponsiveValue(values, containerWidth) {
  const index = breakpoints.findIndex((breakpoint) => breakpoint <= containerWidth);
  return unwrap(values[Math.max(index, 0)], containerWidth);
}
function resolveResponsiveParameter(parameter, containerWidth, values, minValue = 0) {
  if (containerWidth === void 0) return void 0;
  const value = unwrapParameter(parameter, containerWidth);
  return Math.round(Math.max(value === void 0 ? selectResponsiveValue(values, containerWidth) : value, minValue));
}
function resolveCommonProps(containerWidth, {
  spacing,
  padding,
  componentsProps,
  render
}) {
  return {
    spacing: resolveResponsiveParameter(spacing, containerWidth, [20, 15, 10, 5]),
    padding: resolveResponsiveParameter(padding, containerWidth, [0, 0, 0, 0]),
    componentsProps: unwrap(componentsProps, containerWidth) || {},
    render: unwrap(render, containerWidth)
  };
}
function round(value, decimals = 0) {
  const factor = 10 ** decimals;
  return Math.round((value + Number.EPSILON) * factor) / factor;
}
function srcSetAndSizes(photo, responsiveSizes, photoWidth, containerWidth, photosCount, spacing, padding) {
  let srcSet;
  let sizes;
  const calcSizes = (base) => {
    var _a;
    const gaps = spacing * (photosCount - 1) + 2 * padding * photosCount;
    return `calc((${((_a = base.match(/^\s*calc\((.*)\)\s*$/)) == null ? void 0 : _a[1]) ?? base} - ${gaps}px) / ${round((containerWidth - gaps) / photoWidth, 5)})`;
  };
  const images = photo.srcSet;
  if (images && images.length > 0) {
    srcSet = images.concat(
      !images.some(({ width }) => width === photo.width) ? [{ src: photo.src, width: photo.width, height: photo.height }] : []
    ).sort((first, second) => first.width - second.width).map((image) => `${image.src} ${image.width}w`).join(", ");
  }
  if (responsiveSizes == null ? void 0 : responsiveSizes.size) {
    sizes = (responsiveSizes.sizes || []).map(({ viewport, size }) => `${viewport} ${calcSizes(size)}`).concat(calcSizes(responsiveSizes.size)).join(", ");
  } else {
    sizes = `${Math.ceil(photoWidth / containerWidth * 100)}vw`;
  }
  return { srcSet, sizes };
}

// node_modules/react-photo-album/dist/static/index.js
function Component({
  as,
  render,
  context,
  classes = [],
  variables = {},
  style: styleProp,
  className: classNameProp,
  children,
  ...rest
}, ref) {
  const className = clsx(
    ...(Array.isArray(classes) ? classes : [classes]).filter((el) => typeof el === "string").map(cssClass),
    classNameProp
  );
  const style = {
    ...Object.fromEntries(
      Object.entries(variables).map(([key, value]) => [
        cssVar(key.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase()),
        typeof value === "number" ? round(value, 5) : value
      ])
    ),
    ...styleProp
  };
  const props = { style, className, children, ...rest };
  if (render) {
    const rendered = render({ ref, ...props }, context);
    if (rendered) return rendered;
  }
  const Element = as || "div";
  return (0, import_jsx_runtime.jsx)(Element, { ref, ...props });
}
var Component$1 = (0, import_react2.forwardRef)(Component);
function PhotoComponent({
  photo,
  index,
  width,
  height,
  onClick,
  render: { wrapper, link, button, image, extras } = {},
  componentsProps: { link: linkProps, button: buttonProps, wrapper: wrapperProps, image: imageProps } = {}
}, ref) {
  const { href } = photo;
  const context = { photo, index, width: round(width, 3), height: round(height, 3) };
  let props;
  if (href) {
    props = { ...linkProps, as: "a", render: link, classes: ["photo", "link"], href, onClick };
  } else if (onClick) {
    props = { ...buttonProps, as: "button", type: "button", render: button, classes: ["photo", "button"], onClick };
  } else {
    props = { ...wrapperProps, render: wrapper, classes: "photo" };
  }
  return (0, import_jsx_runtime.jsxs)(
    Component$1,
    {
      ref,
      variables: { photoWidth: context.width, photoHeight: context.height },
      ...{ context, ...props },
      children: [
        (0, import_jsx_runtime.jsx)(Component$1, { as: "img", classes: "image", render: image, context, ...imageProps }),
        extras == null ? void 0 : extras({}, context)
      ]
    }
  );
}
var PhotoComponent$1 = (0, import_react2.forwardRef)(PhotoComponent);
function StaticPhotoAlbum({
  layout,
  sizes,
  model,
  skeleton,
  onClick: onClickCallback,
  render: { container, track, photo: renderPhoto, ...restRender } = {},
  componentsProps: {
    container: containerProps,
    track: trackProps,
    link: linkProps,
    button: buttonProps,
    wrapper: wrapperProps,
    image: imageProps
  } = {}
}, ref) {
  const { spacing, padding, containerWidth, tracks, variables, horizontal } = model || {};
  return (0, import_jsx_runtime.jsxs)(
    Component$1,
    {
      role: "group",
      "aria-label": "Photo album",
      ...containerProps,
      variables: { spacing, padding, containerWidth, ...variables },
      classes: ["", layout],
      render: container,
      ref,
      children: [
        spacing !== void 0 && padding !== void 0 && containerWidth !== void 0 && (tracks == null ? void 0 : tracks.map(({ photos, variables: trackVariables }, trackIndex) => {
          const trackSize = photos.length;
          const photosCount = horizontal ? trackSize : tracks.length;
          return (0, import_react2.createElement)(
            Component$1,
            {
              ...trackProps,
              key: trackIndex,
              render: track,
              classes: "track",
              variables: { trackSize, ...trackVariables }
            },
            photos.map((context) => {
              const { photo, index, width } = context;
              const { key, src, alt, title, label } = photo;
              const onClick = onClickCallback ? (event) => {
                onClickCallback({ event, photo, index });
              } : void 0;
              if (renderPhoto) {
                const rendered = renderPhoto({ onClick }, context);
                if (rendered) return rendered;
              }
              const ariaLabel = (props) => {
                return label ? { "aria-label": label, ...props } : props;
              };
              return (0, import_jsx_runtime.jsx)(
                PhotoComponent$1,
                {
                  onClick,
                  render: restRender,
                  componentsProps: {
                    image: {
                      loading: "lazy",
                      decoding: "async",
                      src,
                      alt,
                      title,
                      ...srcSetAndSizes(photo, sizes, width, containerWidth, photosCount, spacing, padding),
                      ...unwrap(imageProps, context)
                    },
                    link: ariaLabel(unwrap(linkProps, context)),
                    button: ariaLabel(unwrap(buttonProps, context)),
                    wrapper: unwrap(wrapperProps, context)
                  },
                  ...context
                },
                key ?? src
              );
            })
          );
        })),
        containerWidth === void 0 && skeleton
      ]
    }
  );
}
var StaticPhotoAlbum$1 = (0, import_react2.forwardRef)(StaticPhotoAlbum);

// node_modules/react-photo-album/dist/client/rowsProps.js
function resolveRowsProps(containerWidth, { photos, targetRowHeight, rowConstraints, ...rest }) {
  const { spacing, padding, componentsProps, render } = resolveCommonProps(containerWidth, rest);
  const { singleRowMaxHeight, minPhotos, maxPhotos } = unwrapParameter(rowConstraints, containerWidth) || {};
  if (singleRowMaxHeight !== void 0 && spacing !== void 0 && padding !== void 0) {
    const maxWidth = Math.floor(
      photos.reduce(
        (acc, { width, height }) => acc + width / height * singleRowMaxHeight - 2 * padding,
        padding * photos.length * 2 + spacing * (photos.length - 1)
      )
    );
    if (maxWidth > 0) {
      componentsProps.container = { ...componentsProps.container };
      componentsProps.container.style = { maxWidth, ...componentsProps.container.style };
    }
  }
  return {
    ...rest,
    targetRowHeight: resolveResponsiveParameter(targetRowHeight, containerWidth, [
      (w) => w / 5,
      (w) => w / 4,
      (w) => w / 3,
      (w) => w / 2
    ]),
    render,
    spacing,
    padding,
    minPhotos,
    maxPhotos,
    componentsProps
  };
}

// node_modules/react-photo-album/dist/layouts/rows.js
function rankingFunctionComparator(rank) {
  return (a, b) => rank(b) - rank(a);
}
function MinHeap(comparator) {
  let n = 0;
  const heap = [];
  const greater = (i, j) => comparator(heap[i], heap[j]) < 0;
  const swap = (i, j) => {
    const temp = heap[i];
    heap[i] = heap[j];
    heap[j] = temp;
  };
  const swim = (i) => {
    let k = i;
    let k2 = Math.floor(k / 2);
    while (k > 1 && greater(k2, k)) {
      swap(k2, k);
      k = k2;
      k2 = Math.floor(k / 2);
    }
  };
  const sink = (i) => {
    let k = i;
    let k2 = k * 2;
    while (k2 <= n) {
      if (k2 < n && greater(k2, k2 + 1)) k2 += 1;
      if (!greater(k, k2)) break;
      swap(k, k2);
      k = k2;
      k2 = k * 2;
    }
  };
  const push = (element) => {
    n += 1;
    heap[n] = element;
    swim(n);
  };
  const pop = () => {
    if (n === 0) return void 0;
    swap(1, n);
    n -= 1;
    const max = heap.pop();
    sink(1);
    return max;
  };
  const size = () => n;
  return { push, pop, size };
}
function buildPrecedentsMap(graph, startNode, endNode) {
  const precedentsMap = /* @__PURE__ */ new Map();
  const visited = /* @__PURE__ */ new Set();
  const storedShortestPaths = /* @__PURE__ */ new Map();
  storedShortestPaths.set(startNode, 0);
  const queue = MinHeap(rankingFunctionComparator((el) => el[1]));
  queue.push([startNode, 0]);
  while (queue.size() > 0) {
    const [id, weight] = queue.pop();
    if (!visited.has(id)) {
      const neighboringNodes = graph(id);
      visited.add(id);
      neighboringNodes.forEach((neighborWeight, neighbor) => {
        const newWeight = weight + neighborWeight;
        const currentId = precedentsMap.get(neighbor);
        const currentWeight = storedShortestPaths.get(neighbor);
        if (currentWeight === void 0 || currentWeight > newWeight && (currentWeight / newWeight > 1.005 || currentId !== void 0 && currentId < id)) {
          storedShortestPaths.set(neighbor, newWeight);
          queue.push([neighbor, newWeight]);
          precedentsMap.set(neighbor, id);
        }
      });
    }
  }
  return storedShortestPaths.has(endNode) ? precedentsMap : void 0;
}
function getPathFromPrecedentsMap(precedentsMap, endNode) {
  if (!precedentsMap) return void 0;
  const nodes = [];
  for (let node = endNode; node !== void 0; node = precedentsMap.get(node)) {
    nodes.push(node);
  }
  return nodes.reverse();
}
function findShortestPath(graph, startNode, endNode) {
  return getPathFromPrecedentsMap(buildPrecedentsMap(graph, startNode, endNode), endNode);
}
function findIdealNodeSearch(photos, containerWidth, targetRowHeight, minPhotos) {
  return round(containerWidth / targetRowHeight / Math.min(...photos.map((photo) => ratio(photo)))) + (minPhotos || 0) + 2;
}
function getCommonHeight(photos, containerWidth, spacing, padding) {
  return (containerWidth - (photos.length - 1) * spacing - 2 * padding * photos.length) / photos.reduce((acc, photo) => acc + ratio(photo), 0);
}
function cost(photos, i, j, width, spacing, padding, targetRowHeight) {
  const row = photos.slice(i, j);
  const commonHeight = getCommonHeight(row, width, spacing, padding);
  return commonHeight > 0 ? (commonHeight - targetRowHeight) ** 2 * row.length : void 0;
}
function makeGetRowNeighbors(photos, spacing, padding, containerWidth, targetRowHeight, limitNodeSearch, minPhotos, maxPhotos) {
  return (node) => {
    const results = /* @__PURE__ */ new Map();
    results.set(node, 0);
    const startOffset = minPhotos || 1;
    const endOffset = Math.min(limitNodeSearch, maxPhotos || Infinity);
    for (let i = node + startOffset; i < photos.length + 1; i += 1) {
      if (i - node > endOffset) break;
      const currentCost = cost(photos, node, i, containerWidth, spacing, padding, targetRowHeight);
      if (currentCost === void 0) break;
      results.set(i, currentCost);
    }
    return results;
  };
}
function computeRowsLayout(photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos) {
  const limitNodeSearch = findIdealNodeSearch(photos, containerWidth, targetRowHeight, minPhotos);
  const getNeighbors = makeGetRowNeighbors(
    photos,
    spacing,
    padding,
    containerWidth,
    targetRowHeight,
    limitNodeSearch,
    minPhotos,
    maxPhotos
  );
  const path = findShortestPath(getNeighbors, 0, photos.length);
  if (!path) return void 0;
  const tracks = [];
  for (let i = 1; i < path.length; i += 1) {
    const row = photos.map((photo, index) => ({ photo, index })).slice(path[i - 1], path[i]);
    const height = getCommonHeight(
      row.map(({ photo }) => photo),
      containerWidth,
      spacing,
      padding
    );
    tracks.push({
      photos: row.map(({ photo, index }) => ({
        photo,
        index,
        width: height * ratio(photo),
        height
      }))
    });
  }
  return { spacing, padding, containerWidth, tracks, horizontal: true };
}

// node_modules/react-photo-album/dist/client/rows.js
function RowsPhotoAlbum({ photos, breakpoints: breakpoints2, defaultContainerWidth, ...rest }, ref) {
  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints2, defaultContainerWidth);
  const { spacing, padding, targetRowHeight, minPhotos, maxPhotos, ...restProps } = resolveRowsProps(containerWidth, {
    photos,
    ...rest
  });
  const model = (0, import_react3.useMemo)(
    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && targetRowHeight !== void 0 ? computeRowsLayout(photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos) : void 0,
    [photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos]
  );
  return (0, import_jsx_runtime2.jsx)(StaticPhotoAlbum$1, { layout: "rows", ref: containerRef, model, ...restProps });
}
var RowsPhotoAlbum$1 = (0, import_react3.forwardRef)(RowsPhotoAlbum);

// node_modules/react-photo-album/dist/client/columns.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);

// node_modules/react-photo-album/dist/client/columnsProps.js
function resolveColumnsProps(containerWidth, { columns, ...rest }) {
  return {
    ...rest,
    ...resolveCommonProps(containerWidth, rest),
    columns: resolveResponsiveParameter(columns, containerWidth, [5, 4, 3, 2], 1)
  };
}

// node_modules/react-photo-album/dist/layouts/columns.js
function computeShortestPath(graph, pathLength, startNode, endNode) {
  const matrix = /* @__PURE__ */ new Map();
  const queue = /* @__PURE__ */ new Set();
  queue.add(startNode);
  for (let length = 0; length < pathLength; length += 1) {
    const currentQueue = [...queue.keys()];
    queue.clear();
    currentQueue.forEach((node) => {
      const accumulatedWeight = length > 0 ? matrix.get(node)[length][1] : 0;
      graph(node).forEach(([neighbor, weight]) => {
        let paths = matrix.get(neighbor);
        if (!paths) {
          paths = [];
          matrix.set(neighbor, paths);
        }
        const newWeight = accumulatedWeight + weight;
        const nextPath = paths[length + 1];
        if (!nextPath || nextPath[1] > newWeight && (nextPath[1] / newWeight > 1.0001 || node < nextPath[0])) {
          paths[length + 1] = [node, newWeight];
        }
        if (length < pathLength - 1 && neighbor !== endNode) {
          queue.add(neighbor);
        }
      });
    });
  }
  return matrix;
}
function reconstructShortestPath(matrix, pathLength, endNode) {
  const path = [endNode];
  for (let node = endNode, length = pathLength; length > 0; length -= 1) {
    [node] = matrix.get(node)[length];
    path.push(node);
  }
  return path.reverse();
}
function findShortestPathLengthN(graph, pathLength, startNode, endNode) {
  return reconstructShortestPath(computeShortestPath(graph, pathLength, startNode, endNode), pathLength, endNode);
}
function makeGetColumnNeighbors(photos, spacing, padding, targetColumnWidth, targetColumnHeight) {
  return (node) => {
    const results = [];
    const cutOffHeight = targetColumnHeight * 1.5;
    let height = targetColumnWidth / ratio(photos[node]) + 2 * padding;
    for (let i = node + 1; i < photos.length + 1; i += 1) {
      results.push([i, (targetColumnHeight - height) ** 2]);
      if (height > cutOffHeight || i === photos.length) {
        break;
      }
      height += targetColumnWidth / ratio(photos[i]) + spacing + 2 * padding;
    }
    return results;
  };
}
function buildColumnsModel(path, photos, spacing, padding, containerWidth, columnsGaps, columnsRatios) {
  const tracks = [];
  const totalRatio = columnsRatios.reduce((total, columnRatio) => total + columnRatio, 0);
  for (let i = 0; i < path.length - 1; i += 1) {
    const column = photos.map((photo, index) => ({ photo, index })).slice(path[i], path[i + 1]);
    const adjustedGaps = columnsRatios.reduce(
      (total, columnRatio, index) => total + (columnsGaps[i] - columnsGaps[index]) * columnRatio,
      0
    );
    const columnWidth = (containerWidth - (path.length - 2) * spacing - 2 * (path.length - 1) * padding - adjustedGaps) * columnsRatios[i] / totalRatio;
    tracks.push({
      photos: column.map(({ photo, index }) => ({
        photo,
        index,
        width: columnWidth,
        height: columnWidth / ratio(photo)
      })),
      variables: { adjustedGaps, columnRatio: columnsRatios[i] }
    });
  }
  return { tracks, variables: { totalRatio } };
}
function computeColumnsModel(photos, spacing, padding, containerWidth, targetColumnWidth, columns) {
  const columnsGaps = [];
  const columnsRatios = [];
  if (photos.length <= columns) {
    const averageRatio = photos.length > 0 ? photos.reduce((acc, photo) => acc + ratio(photo), 0) / photos.length : 1;
    for (let i = 0; i < columns; i += 1) {
      columnsGaps[i] = 2 * padding;
      columnsRatios[i] = i < photos.length ? ratio(photos[i]) : averageRatio;
    }
    return buildColumnsModel(
      Array.from({ length: columns + 1 }, (_, index) => Math.min(index, photos.length)),
      photos,
      spacing,
      padding,
      containerWidth,
      columnsGaps,
      columnsRatios
    );
  }
  const targetColumnHeight = (photos.reduce((acc, photo) => acc + targetColumnWidth / ratio(photo), 0) + spacing * (photos.length - columns) + 2 * padding * photos.length) / columns;
  const getNeighbors = makeGetColumnNeighbors(photos, spacing, padding, targetColumnWidth, targetColumnHeight);
  const path = findShortestPathLengthN(getNeighbors, columns, 0, photos.length);
  for (let i = 0; i < path.length - 1; i += 1) {
    const column = photos.slice(path[i], path[i + 1]);
    columnsGaps[i] = spacing * (column.length - 1) + 2 * padding * column.length;
    columnsRatios[i] = 1 / column.reduce((acc, photo) => acc + 1 / ratio(photo), 0);
  }
  return buildColumnsModel(path, photos, spacing, padding, containerWidth, columnsGaps, columnsRatios);
}
function computeColumnsLayout(photos, spacing, padding, containerWidth, columns) {
  const targetColumnWidth = (containerWidth - spacing * (columns - 1) - 2 * padding * columns) / columns;
  const { tracks, variables } = computeColumnsModel(
    photos,
    spacing,
    padding,
    containerWidth,
    targetColumnWidth,
    columns
  );
  if (tracks.some((track) => track.photos.some(({ width, height }) => width < 0 || height < 0))) {
    return columns > 1 ? computeColumnsLayout(photos, spacing, padding, containerWidth, columns - 1) : void 0;
  }
  return { tracks, spacing, padding, containerWidth, variables: { columns, ...variables } };
}

// node_modules/react-photo-album/dist/client/columns.js
function ColumnsPhotoAlbum({ photos, breakpoints: breakpoints2, defaultContainerWidth, ...rest }, ref) {
  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints2, defaultContainerWidth);
  const { spacing, padding, columns, ...restProps } = resolveColumnsProps(containerWidth, { photos, ...rest });
  const model = (0, import_react4.useMemo)(
    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && columns !== void 0 ? computeColumnsLayout(photos, spacing, padding, containerWidth, columns) : void 0,
    [photos, spacing, padding, containerWidth, columns]
  );
  return (0, import_jsx_runtime3.jsx)(StaticPhotoAlbum$1, { layout: "columns", ref: containerRef, model, ...restProps });
}
var ColumnsPhotoAlbum$1 = (0, import_react4.forwardRef)(ColumnsPhotoAlbum);

// node_modules/react-photo-album/dist/client/masonry.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);

// node_modules/react-photo-album/dist/client/masonryProps.js
function resolveMasonryProps(containerWidth, { columns, ...rest }) {
  return {
    ...rest,
    ...resolveCommonProps(containerWidth, rest),
    columns: resolveResponsiveParameter(columns, containerWidth, [5, 4, 3, 2], 1)
  };
}

// node_modules/react-photo-album/dist/layouts/masonry.js
function computeMasonryLayout(photos, spacing, padding, containerWidth, columns) {
  const columnWidth = (containerWidth - spacing * (columns - 1) - 2 * padding * columns) / columns;
  if (columnWidth <= 0) {
    return columns > 1 ? computeMasonryLayout(photos, spacing, padding, containerWidth, columns - 1) : void 0;
  }
  const columnsCurrentTopPositions = [];
  for (let i = 0; i < columns; i += 1) {
    columnsCurrentTopPositions[i] = 0;
  }
  const columnsModel = photos.reduce(
    (model, photo, index) => {
      const shortestColumn = columnsCurrentTopPositions.reduce(
        (currentShortestColumn, item, i) => item < columnsCurrentTopPositions[currentShortestColumn] - 1 ? i : currentShortestColumn,
        0
      );
      columnsCurrentTopPositions[shortestColumn] = columnsCurrentTopPositions[shortestColumn] + columnWidth / ratio(photo) + spacing + 2 * padding;
      model[shortestColumn].push({ photo, index });
      return model;
    },
    Array.from({ length: columns }, () => [])
  );
  return {
    spacing,
    padding,
    containerWidth,
    variables: { columns },
    tracks: columnsModel.map((column) => ({
      photos: column.map(({ photo, index }) => ({
        photo,
        index,
        width: columnWidth,
        height: columnWidth / ratio(photo)
      }))
    }))
  };
}

// node_modules/react-photo-album/dist/client/masonry.js
function MasonryPhotoAlbum({ photos, breakpoints: breakpoints2, defaultContainerWidth, ...rest }, ref) {
  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints2, defaultContainerWidth);
  const { spacing, padding, columns, ...restProps } = resolveMasonryProps(containerWidth, { photos, ...rest });
  const model = (0, import_react5.useMemo)(
    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && columns !== void 0 ? computeMasonryLayout(photos, spacing, padding, containerWidth, columns) : void 0,
    [photos, spacing, padding, containerWidth, columns]
  );
  return (0, import_jsx_runtime4.jsx)(StaticPhotoAlbum$1, { layout: "masonry", ref: containerRef, model, ...restProps });
}
var MasonryPhotoAlbum$1 = (0, import_react5.forwardRef)(MasonryPhotoAlbum);

// node_modules/react-photo-album/dist/client/aggregate.js
function PhotoAlbum({ layout, ...rest }, ref) {
  if (layout === "rows") return (0, import_jsx_runtime5.jsx)(RowsPhotoAlbum$1, { ref, ...rest });
  if (layout === "columns") return (0, import_jsx_runtime5.jsx)(ColumnsPhotoAlbum$1, { ref, ...rest });
  if (layout === "masonry") return (0, import_jsx_runtime5.jsx)(MasonryPhotoAlbum$1, { ref, ...rest });
  return null;
}
var PhotoAlbum$1 = (0, import_react6.forwardRef)(PhotoAlbum);
export {
  ColumnsPhotoAlbum$1 as ColumnsPhotoAlbum,
  MasonryPhotoAlbum$1 as MasonryPhotoAlbum,
  RowsPhotoAlbum$1 as RowsPhotoAlbum,
  StaticPhotoAlbum$1 as StaticPhotoAlbum,
  computeColumnsLayout,
  computeMasonryLayout,
  computeRowsLayout,
  PhotoAlbum$1 as default
};
//# sourceMappingURL=react-photo-album.js.map
