import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect } from "react";
import { BlogStatus } from "@/lib/types/blog";

// Định nghĩa schema cho form
const formSchema = z.object({
  title: z.string().min(1, "Tiêu đề không được để trống").max(100, "Tiêu đề không được vượt quá 100 ký tự"),
  content: z.string().min(1, "Nội dung không được để trống"),
  featuredImage: z.string().url("URL ảnh không hợp lệ").optional().or(z.literal("")),
  tags: z.string().optional(),
  status: z.string(),
  publishedAt: z.string().nullable().optional(),
});

export type BlogFormValues = z.infer<typeof formSchema>;

interface BlogPostFormProps {
  defaultValues?: Partial<BlogFormValues>;
  onSubmit: (data: BlogFormValues) => void;
  isSubmitting: boolean;
}

export function BlogPostForm({
  defaultValues,
  onSubmit,
  isSubmitting,
}: BlogPostFormProps) {
  
  const defaultFormValues: BlogFormValues = {
    title: "",
    content: "",
    featuredImage: "",
    tags: "",
    status: BlogStatus.DRAFT,
    publishedAt: null,
    ...defaultValues
  };
  
  const form = useForm<BlogFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  });

  // Cập nhật form khi defaultValues thay đổi
  useEffect(() => {
    if (defaultValues) {
      const updatedValues = {
        ...defaultFormValues,
        ...defaultValues
      };
      form.reset(updatedValues);
    }
  }, [defaultValues]);

  const handleStatusChange = (value: string) => {
    form.setValue("status", value);
    
    // Tự động cập nhật ngày xuất bản nếu trạng thái là "published"
    if (value === BlogStatus.PUBLISHED && !form.getValues("publishedAt")) {
      form.setValue("publishedAt", new Date().toISOString());
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tiêu đề</FormLabel>
              <FormControl>
                <Input placeholder="Nhập tiêu đề bài viết" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nội dung</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Nhập nội dung bài viết"
                  className="min-h-[300px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Hỗ trợ định dạng HTML cơ bản. Để có trải nghiệm tốt hơn, bạn có thể sử dụng trình soạn thảo văn bản phong phú bên ngoài và dán vào đây.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="featuredImage"
          render={({ field }) => (
            <FormItem>
              <FormLabel>URL ảnh đại diện</FormLabel>
              <FormControl>
                <Input placeholder="https://example.com/image.jpg" {...field} />
              </FormControl>
              <FormMessage />
              {field.value && (
                <div className="mt-2">
                  <img 
                    src={field.value} 
                    alt="Ảnh xem trước" 
                    className="max-h-40 rounded-md object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
              )}
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="tags"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Thẻ (phân cách bằng dấu phẩy)</FormLabel>
              <FormControl>
                <Input placeholder="tin tức, sự kiện, bất động sản" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Trạng thái</FormLabel>
              <Select
                onValueChange={handleStatusChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn trạng thái" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value={BlogStatus.DRAFT}>Bản nháp</SelectItem>
                  <SelectItem value={BlogStatus.PUBLISHED}>Đã xuất bản</SelectItem>
                  <SelectItem value={BlogStatus.ARCHIVED}>Đã lưu trữ</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button 
            type="button" 
            variant="outline"
            onClick={() => window.history.back()}
          >
            Hủy
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Đang lưu..." : "Lưu bài viết"}
          </Button>
        </div>
      </form>
    </Form>
  );
} 