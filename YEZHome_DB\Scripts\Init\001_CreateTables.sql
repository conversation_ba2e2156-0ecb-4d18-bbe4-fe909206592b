 
-- Users Table
CREATE TABLE IF NOT EXISTS "AppUser" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "FullName" TEXT NOT NULL,
    "Email" VARCHAR(255) UNIQUE NOT NULL,
    "PasswordHash" VARCHAR(255) NOT NULL,
    "PasswordSalt" VARCHAR(255) NOT NULL,
    "UserType" VARCHAR(50) CHECK ("UserType" IN ('Seller', 'Buyer', 'Admin')),
    "Phone" VARCHAR(11) NOT NULL,
    "Phone2" VARCHAR(11) NULL,
    "Phone3" VARCHAR(11) NULL,
    "LastLogin" TIMESTAMP NULL,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMES<PERSON>MP NULL,
    "TotalSpent" NUMERIC(20,2) DEFAULT 0, -- Tổng số tiền đã sử dụng
    "MemberRank" VARCHAR(50) NOT NULL DEFAULT 'default' CHECK ("MemberRank" IN ('default', 'bronze', 'sliver', 'gold', 'platinum', 'diamond')),
    "AvatarImage" VARCHAR(255) NULL,
    "TransferCode" VARCHAR(20) UNIQUE
);

-- Admin Roles Table
CREATE TABLE IF NOT EXISTS "AdminRole" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "Code" VARCHAR(20) UNIQUE NOT NULL,
    "RoleName" VARCHAR(100) UNIQUE NOT NULL,
    "Description" TEXT NULL
);

-- User-Role Mapping Table
CREATE TABLE IF NOT EXISTS "UserRole" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "UserID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "RoleID" UUID NOT NULL REFERENCES "AdminRole"("Id") ON DELETE CASCADE
);

-- Location Tables
CREATE TABLE IF NOT EXISTS "City" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(255) NOT NULL,
    "Type" VARCHAR(20) NOT NULL,
    "NameWithType" VARCHAR(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS "District" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL, --"Long Xuyên",
    "Slug" VARCHAR(255) NOT NULL, -- "long-xuyen",
    "Type" VARCHAR(20) NOT NULL, -- "thanh-pho",
    "NameWithType" VARCHAR(255) NOT NULL, -- "Thành phố Long Xuyên",
    "Path" VARCHAR(255) NOT NULL, -- "Long Xuyên, An Giang",
    "PathWithType" VARCHAR(255) NOT NULL, -- "Thành phố Long Xuyên, Tỉnh An Giang",
    "CityId" INTEGER REFERENCES "City"("Id")
);

CREATE TABLE IF NOT EXISTS "Ward" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(255) NOT NULL, -- "my-binh",
    "Type" VARCHAR(20) NOT NULL, -- "phuong",
    "NameWithType" VARCHAR(255) NOT NULL, -- "Phường Mỹ Bình",
    "Path" VARCHAR(255) NOT NULL, -- "Mỹ Bình, Long Xuyên, An Giang",
    "PathWithType" VARCHAR(255) NOT NULL, -- "Phường Mỹ Bình, Thành phố Long Xuyên, Tỉnh An Giang",
    "DistrictId" INTEGER REFERENCES "District"("Id")
);

CREATE TABLE IF NOT EXISTS "Street" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL,
    "DistrictId" INTEGER REFERENCES "District"("Id")
);

-- Project Table
CREATE TABLE IF NOT EXISTS "Projects" (
    "Id" INTEGER PRIMARY KEY,
    "ProjectName" VARCHAR(255),
    "WardId" INTEGER REFERENCES "Ward"("Id"),
    "StreetId" INTEGER REFERENCES "Street"("Id")
);

--  Table
CREATE TABLE IF NOT EXISTS "Property" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "OwnerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Name" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(255) NOT NULL,
    "PostType" VARCHAR(5) NOT NULL,
    "PropertyType" VARCHAR(20) NOT NULL,
    "CityId" INTEGER REFERENCES "City"("Id"),
    "DistrictId" INTEGER REFERENCES "District"("Id"),
    "StreetId" INTEGER REFERENCES "Street"("Id"),
    "WardId" INTEGER REFERENCES "Ward"("Id"),
    "Address" VARCHAR(500) NOT NULL, -- địa chỉ 
    "Area" NUMERIC(10,2), -- diện tích
    "Price" NUMERIC(18,2) NOT NULL, -- giá
    "Latitude" DECIMAL(9,6) NOT NULL,
    "Longitude" DECIMAL(9,6) NOT NULL,
    "VideoUrl" VARCHAR(500),
    "Floors" INTEGER, -- tầng 
    "Rooms" INTEGER, -- số phòng ngủ
    "Toilets" INTEGER, -- số toalet
    "Direction" VARCHAR(20), -- hướng nhà 
    "BalconyDirection" VARCHAR(20), -- hướng ban công
    "Legality" VARCHAR(30), -- tình trạng pháp lý (sổ đỏ/sổ hồng, ...)
    "Interior" VARCHAR(30), -- nội thất (đầy đủ, cơ bản, nhà trống)
    "Width" INTEGER, -- mặt tiền rộng ...m
    "RoadWidth" INTEGER, -- đường vào rộng ...m
    "Description" TEXT, -- mô tả 
    "Overview" TEXT, -- tóm tắt 
    "PlaceData" TEXT, -- 
    "Policies" TEXT, -- quy định (vd không nuôi chó, không nuôi heo, ...)
    "Neighborhood" TEXT, -- tiện ích xung quanh (vd gần bệnh viện, trường học, có thể add vào chung phần mô tả, phần này add thêm, add đâu cũng được)
    "Status" VARCHAR(20) DEFAULT 'draft', -- CHECK ("Status" IN ('Pending', 'Approved', 'Rejected', 'Draft')),    
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL,
    "PostPrice" NUMERIC(20,2) NOT NULL DEFAULT 55000, -- Phí đăng bài
    "IsHighlighted" BOOLEAN DEFAULT FALSE,
    "IsAutoRenew" BOOLEAN DEFAULT FALSE,
    "ExpiresAt" TIMESTAMP DEFAULT (now() + interval '10 days'),
    "UpdateRemainingTimes" INTEGER -- số lần được chỉnh sửa sau khi review (tối đa 5 lần)
);

CREATE TABLE IF NOT EXISTS "PropertyInvoice"
(
    "Id" uuid NOT NULL,
    "InvoiceCode" character varying(50) COLLATE pg_catalog."default" NOT NULL,
    "PropertyId"  UUID NOT NULL REFERENCES "Property"("Id"),
    "IsHighlighted" boolean DEFAULT false,
    "Price" numeric NOT NULL DEFAULT 55000,
    "HighlightFee" numeric DEFAULT 0,
    "Total" numeric,
    "CreatedBy" uuid,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "PropertyInvoice_pkey" PRIMARY KEY ("Id")
);

-- Contact Requests Table
CREATE TABLE IF NOT EXISTS "ContactRequest" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "Name" VARCHAR(255) NOT NULL,
    "Email" VARCHAR(255) NOT NULL,
    "Phone" VARCHAR(20) NOT NULL,
    "SentAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "AgentId" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "UserId" UUID NULL REFERENCES "AppUser"("Id") ON DELETE SET NULL,
    "Note" TEXT NULL, -- Ghi chú thêm từ khách hàng hoặc agent
    "Status" VARCHAR(20) DEFAULT 'Pending',
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Indexes for faster queries
CREATE INDEX "IDX_ContactRequest_Property" ON "ContactRequest"("PropertyId");
CREATE INDEX "IDX_ContactRequest_Agent" ON "ContactRequest"("AgentId");
CREATE INDEX "IDX_ContactRequest_User" ON "ContactRequest"("UserId");
CREATE INDEX "IDX_ContactRequest_SentAt" ON "ContactRequest"("SentAt");
CREATE INDEX "IDX_ContactRequest_Status" ON "ContactRequest"("Status");

-- Property Reviews Table
CREATE TABLE IF NOT EXISTS "PropertyReview" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PropertyID" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "BuyerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Rating" INT CHECK ("Rating" BETWEEN 1 AND 5),
    "ReviewText" TEXT,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Owner Reviews Table
CREATE TABLE IF NOT EXISTS "OwnerReview" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "OwnerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "BuyerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Rating" INT CHECK ("Rating" BETWEEN 1 AND 5),
    "ReviewText" TEXT,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Property Status Logs Table
CREATE TABLE IF NOT EXISTS "PropertyStatusLog" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PropertyID" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "Status" VARCHAR(50),
    "ChangedBy" UUID NOT NULL REFERENCES "AppUser"("Id"),
    "ChangedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "Comment" TEXT
);

-- Blog Posts Table
CREATE TABLE IF NOT EXISTS "BlogPost" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "AuthorID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Title" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(500) NOT NULL,
    "Content" TEXT NOT NULL,
    "FeaturedImage" VARCHAR(500),
    "Tags" VARCHAR(255),
    "IsFeature" BOOLEAN DEFAULT FALSE,
    "Status" VARCHAR(50) DEFAULT 'Draft' CHECK ("Status" IN ('draft', 'published', 'archived')),
    "PublishedAt" TIMESTAMP NULL,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Blog Comments Table
CREATE TABLE IF NOT EXISTS "BlogComment" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PostID" UUID NOT NULL REFERENCES "BlogPost"("Id") ON DELETE CASCADE,
    "UserID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "CommentText" TEXT NOT NULL,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Media Table for Property (Images/Videos)
CREATE TABLE IF NOT EXISTS "PropertyMedia" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PropertyID" UUID NULL,
    "MediaType" VARCHAR(20) NOT NULL,
    "MediaURL" VARCHAR(500) NOT NULL,
    "UploadedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "Caption" VARCHAR(50),
    "IsAvatar" BOOLEAN DEFAULT FALSE,
    "FilePath" TEXT NULL,
    "ThumbnailPath" TEXT NULL,
    "SmallPath" TEXT NULL,
    "MediumPath" TEXT NULL,
    "LargePath" TEXT NULL
);

-- Favorites Table for Users to Follow 
CREATE TABLE IF NOT EXISTS "UserFavorite" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "UserID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "PropertyID" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_favorite UNIQUE ("UserID", "PropertyID")
);

CREATE TABLE IF NOT EXISTS "Wallets" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID UNIQUE NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Balance" NUMERIC(20,2) DEFAULT 0 -- Số dư ví
);

CREATE TABLE IF NOT EXISTS "WalletTransactions" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Amount" NUMERIC(20,2) NOT NULL,
    "Type" VARCHAR(50) NOT NULL CHECK ("Type" IN ('TOP_UP', 'PAYMENT_POST', 'PAYMENT_HIGHLIGHT')), --type = 'TOP_UP' → Nạp tiền 🔹 type = 'PAYMENT_POST' → Tiêu tiền 🔹 type = 'PAYMENT_HIGHLIGHT' → Tiêu tiền
    "Description" TEXT NOT NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS "MemberRankings" (
    "Id" UUID PRIMARY KEY,
    "RankName" VARCHAR(50) NOT NULL UNIQUE,
    "MinSpent" NUMERIC(20,2),
    "MaxSpent" NUMERIC(20,2)
);

CREATE TABLE IF NOT EXISTS "HighlightFees" (
    "Id" UUID PRIMARY KEY,
    "RankName" VARCHAR(50) NOT NULL UNIQUE,
    "Fee" NUMERIC(20,2) NOT NULL
);

CREATE TABLE IF NOT EXISTS "Notification" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,  -- NULL nếu là thông báo chung
    "Type" VARCHAR(50) NOT NULL,
    "Title" VARCHAR(255) NOT NULL,
    "Message" TEXT NOT NULL,
    "IsRead" BOOLEAN DEFAULT FALSE,
    "CreatedAt" TIMESTAMP DEFAULT now()
);

CREATE INDEX idx_notifications_user ON "Notification" ("UserId", "IsRead", "CreatedAt" DESC);
CREATE INDEX idx_notifications_type ON "Notification" ("Type");

CREATE TABLE IF NOT EXISTS "NotificationPreferences" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID UNIQUE NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "ReceivePromotions" BOOLEAN DEFAULT TRUE,
    "ReceiveWalletUpdates" BOOLEAN DEFAULT TRUE,
    "ReceiveNews" BOOLEAN DEFAULT TRUE,
    "ReceiveCustomerMessages" BOOLEAN DEFAULT TRUE,
    "CreatedAt" TIMESTAMP DEFAULT now()
);