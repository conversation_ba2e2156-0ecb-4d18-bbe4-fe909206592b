# Kế hoạch triển khai tính năng Quản lý Bài viết Blog

Tài liệu này trình bày kế hoạch chi tiết để triển khai tính năng quản lý bài viết blog (tin tức) cho trang quản trị.

## 1. Tổng quan tính năng

Mục tiêu là cho phép quản trị viên thực hiện các thao tác CRUD (Tạo, Đọc, Cập nhật, Xóa) trên các bài viết blog.

-   **Liệt kê Bài viết:** Xem danh sách phân trang của tất cả các bài viết với khả năng sắp xếp và lọc.
-   **Tạo Bài viết:** Thêm một bài viết mới.
-   **Chỉnh sửa Bài viết:** C<PERSON><PERSON> nhật một bài viết đã có.
-   **Xóa Bài viết:** Xóa một bài viết.
-   **Xem chi tiết Bài viết:** Xem thông tin chi tiết của một bài viết.

## 2. Các Endpoints API

Tính năng sẽ sử dụng các endpoints API sau đây được định nghĩa trong `api-document-swagger.json`:

-   `GET /api/Blog/blog-posts`: Để lấy danh sách các bài viết blog.
-   `GET /api/Blog/{id}`: Để lấy một bài viết theo ID.
-   `POST /api/Blog`: Để tạo một bài viết mới.
-   `PUT /api/Blog/{id}`: Để cập nhật một bài viết đã có.
-   `DELETE /api/Blog/{id}`: Để xóa một bài viết.

## 3. Chi tiết triển khai

### 3.1. Cấu trúc tệp

Các tệp mới sẽ được tạo theo cấu trúc sau:

-   **Types:** `src/lib/types/blog.ts` - Sẽ chứa các interface TypeScript cho `BlogPost` và `CreateBlogPost`.
-   **Features:**
    -   `src/features/blog-management/` - Thư mục chính cho tính năng quản lý blog
      -   `components/` - Các components riêng cho tính năng blog
        -   `blog-posts-table.tsx`: Bảng dữ liệu để hiển thị các bài viết blog.
        -   `blog-post-form.tsx`: Form để tạo và chỉnh sửa các bài viết blog.
      -   `hooks/use-blog.ts` - Custom hooks cho tính năng blog (nếu cần).
-   **Services:** `src/services/blog-service.ts` - Các hàm để tương tác với các endpoints API của blog.
-   **Routes/Pages:**
    -   `src/routes/_authenticated/blog/index.tsx`: Trang chính để liệt kê các bài viết blog.
    -   `src/routes/_authenticated/blog/new.tsx`: Trang để tạo một bài viết mới.
    -   `src/routes/_authenticated/blog/$blogId.tsx`: Trang để xem chi tiết một bài viết.
    -   `src/routes/_authenticated/blog/$blogId/edit.tsx`: Trang để chỉnh sửa một bài viết.

### 3.2. Định nghĩa Types (`src/lib/types/blog.ts`)

Dựa trên các schema `BlogPostDto` và `CreateBlogPostDto`.

```typescript
// src/lib/types/blog.ts

export interface BlogPost {
  id: string;
  authorID: string;
  authorName: string;
  title: string;
  slug: string;
  content: string;
  featuredImage?: string;
  tags?: string;
  status: string;
  isFeature: boolean;
  publishedAt?: string;
  blogComments?: any[]; // Định nghĩa BlogComment nếu cần
}

export interface CreateBlogPost {
  authorID: string;
  title: string;
  content: string;
  featuredImage?: string;
  tags?: string;
  status?: string;
}

export interface PagedBlogPosts {
    items: BlogPost[];
    totalCount: number;
    pageCount: number;
    currentPage: number;
    pageSize: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
}
```

### 3.3. API Service (`src/services/blog-service.ts`)

Service này sẽ đóng gói tất cả các lời gọi API liên quan đến bài viết blog.

```typescript
// src/services/blog-service.ts
import apiClient from './axios-config';
import { PagedBlogPosts, BlogPost, CreateBlogPost } from '@/lib/types/blog';

const getBlogPosts = async (params: any): Promise<PagedBlogPosts> => {
  const response = await apiClient.get('/Blog/blog-posts', { params });
  return response.data;
};

const getBlogPost = async (id: string): Promise<BlogPost> => {
    const response = await apiClient.get(`/Blog/${id}`);
    return response.data;
};

const createBlogPost = async (data: CreateBlogPost): Promise<BlogPost> => {
    const response = await apiClient.post('/Blog', data);
    return response.data;
};

const updateBlogPost = async (id: string, data: CreateBlogPost): Promise<void> => {
    await apiClient.put(`/Blog/${id}`, data);
};

const deleteBlogPost = async (id: string): Promise<void> => {
    await apiClient.delete(`/Blog/${id}`);
};

export const blogService = {
  getBlogPosts,
  getBlogPost,
  createBlogPost,
  updateBlogPost,
  deleteBlogPost,
};
```

### 3.4. Routing và UI

-   **Danh sách bài viết (`src/routes/_authenticated/blog/index.tsx`)**
    -   Sử dụng `@tanstack/react-table` cho bảng dữ liệu.
    -   Bảng sử dụng ShadcnUI table trong folder `src/components/ui/table.tsx` để render UI
    -   Sử dụng `useQuery` của `@tanstack/react-query` để lấy dữ liệu bài viết qua `blogService.getBlogPosts`.
    -   Triển khai phân trang, sắp xếp và lọc.
    -   Bao gồm nút "Tạo bài viết mới" liên kết đến `/blog/new`.
    -   Mỗi hàng sẽ có các nút hành động "Chỉnh sửa" và "Xóa".

-   **Form Tạo/Chỉnh sửa (`src/features/blog-management/components/blog-post-form.tsx`, được sử dụng bởi `new.tsx` và `edit.tsx`)**
    -   Sử dụng `react-hook-form` để quản lý trạng thái form và xác thực.
    -   Form sẽ có các trường: Tiêu đề, Nội dung (có thể là một trình soạn thảo văn bản giàu tính năng như Tiptap/Editor.js), URL ảnh đại diện, Thẻ, và Trạng thái (ví dụ: Bản nháp, Đã xuất bản).
    -   Khi submit, nó sẽ gọi `blogService.createBlogPost` hoặc `blogService.updateBlogPost`.
    -   Sử dụng `useMutation` từ `@tanstack/react-query` cho các hoạt động tạo/cập nhật.

-   **Trang chi tiết bài viết (`src/routes/_authenticated/blog/$blogId.tsx`)**
    -   Đây là tùy chọn cho trang quản trị, nhưng tốt cho sự hoàn chỉnh.
    -   Nó sẽ hiển thị nội dung đã định dạng của một bài viết.
    -   Dữ liệu được lấy bằng `useQuery` với `blogService.getBlogPost(blogId)`.

### 3.5. Quản lý Trạng thái

-   `@tanstack/query` sẽ xử lý trạng thái máy chủ (caching, refetching, v.v.).
-   `@tanstack/react-router` sẽ quản lý trạng thái URL cho phân trang và bộ lọc.
-   `react-hook-form` sẽ quản lý trạng thái form cục bộ.

## 4. Các công việc

1.  **Cài đặt:**
    -   Tạo thư mục mới `src/features/blog-management`.
    -   Tạo `src/lib/types/blog.ts`.
    -   Tạo `src/services/blog-service.ts`.
2.  **Types & Services:**
    -   Triển khai các interface trong `blog.ts`.
    -   Triển khai các hàm API trong `blog-service.ts`.
3.  **Trang danh sách bài viết:**
    -   Tạo `src/routes/_authenticated/blog/index.tsx`.
    -   Triển khai component bảng dữ liệu `src/features/blog-management/components/blog-posts-table.tsx`.
    -   Tích hợp lấy dữ liệu, phân trang, sắp xếp và lọc.
    -   Thêm chức năng xóa với hộp thoại xác nhận.
4.  **Trang tạo/chỉnh sửa bài viết:**
    -   Tạo component form có thể tái sử dụng `src/features/blog-management/components/blog-post-form.tsx`.
    -   Tạo `src/routes/_authenticated/blog/new.tsx` để sử dụng form cho việc tạo mới.
    -   Tạo `src/routes/_authenticated/blog/$blogId/edit.tsx` để sử dụng form cho việc chỉnh sửa.
6.  **Đánh giá và Tinh chỉnh:**
    -   Đảm bảo tất cả mã nguồn tuân thủ các quy ước và thực tiễn tốt nhất của dự án.
    -   Kiểm tra luồng CRUD hoàn chỉnh. 