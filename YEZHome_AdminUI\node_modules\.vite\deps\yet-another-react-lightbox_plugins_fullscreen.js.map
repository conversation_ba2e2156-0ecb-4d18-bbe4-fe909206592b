{"version": 3, "sources": ["../../yet-another-react-lightbox/dist/plugins/fullscreen/index.js"], "sourcesContent": ["import * as React from 'react';\nimport { makeUseContext, useDocumentContext, useLayoutEffect, cleanup, useEventCallback, clsx, cssClass, createIcon, useLightboxProps, IconButton, addToolbarButton, createModule } from '../../index.js';\nimport { PLUGIN_FULLSCREEN, CLASS_FULLSIZE, PLUGIN_THUMBNAILS, MODULE_CONTROLLER } from '../../types.js';\n\nconst defaultFullscreenProps = {\n    auto: false,\n    ref: null,\n};\nconst resolveFullscreenProps = (fullscreen) => ({\n    ...defaultFullscreenProps,\n    ...fullscreen,\n});\n\nconst FullscreenContext = React.createContext(null);\nconst useFullscreen = makeUseContext(\"useFullscreen\", \"FullscreenContext\", FullscreenContext);\nfunction FullscreenContextProvider({ fullscreen: fullscreenProps, on, children }) {\n    const { auto, ref } = resolveFullscreenProps(fullscreenProps);\n    const containerRef = React.useRef(null);\n    const [disabled, setDisabled] = React.useState();\n    const [fullscreen, setFullscreen] = React.useState(false);\n    const wasFullscreen = React.useRef(false);\n    const { getOwnerDocument } = useDocumentContext();\n    useLayoutEffect(() => {\n        var _a, _b, _c, _d;\n        const ownerDocument = getOwnerDocument();\n        setDisabled(!((_d = (_c = (_b = (_a = ownerDocument.fullscreenEnabled) !== null && _a !== void 0 ? _a : ownerDocument.webkitFullscreenEnabled) !== null && _b !== void 0 ? _b : ownerDocument.mozFullScreenEnabled) !== null && _c !== void 0 ? _c : ownerDocument.msFullscreenEnabled) !== null && _d !== void 0 ? _d : false));\n    }, [getOwnerDocument]);\n    const getFullscreenElement = React.useCallback(() => {\n        var _a;\n        const ownerDocument = getOwnerDocument();\n        const fullscreenElement = ownerDocument.fullscreenElement ||\n            ownerDocument.webkitFullscreenElement ||\n            ownerDocument.mozFullScreenElement ||\n            ownerDocument.msFullscreenElement;\n        return ((_a = fullscreenElement === null || fullscreenElement === void 0 ? void 0 : fullscreenElement.shadowRoot) === null || _a === void 0 ? void 0 : _a.fullscreenElement) || fullscreenElement;\n    }, [getOwnerDocument]);\n    const enter = React.useCallback(() => {\n        const container = containerRef.current;\n        try {\n            if (container.requestFullscreen) {\n                container.requestFullscreen().catch(() => { });\n            }\n            else if (container.webkitRequestFullscreen) {\n                container.webkitRequestFullscreen();\n            }\n            else if (container.mozRequestFullScreen) {\n                container.mozRequestFullScreen();\n            }\n            else if (container.msRequestFullscreen) {\n                container.msRequestFullscreen();\n            }\n        }\n        catch (_) {\n        }\n    }, []);\n    const exit = React.useCallback(() => {\n        if (!getFullscreenElement())\n            return;\n        const ownerDocument = getOwnerDocument();\n        try {\n            if (ownerDocument.exitFullscreen) {\n                ownerDocument.exitFullscreen().catch(() => { });\n            }\n            else if (ownerDocument.webkitExitFullscreen) {\n                ownerDocument.webkitExitFullscreen();\n            }\n            else if (ownerDocument.mozCancelFullScreen) {\n                ownerDocument.mozCancelFullScreen();\n            }\n            else if (ownerDocument.msExitFullscreen) {\n                ownerDocument.msExitFullscreen();\n            }\n        }\n        catch (_) {\n        }\n    }, [getFullscreenElement, getOwnerDocument]);\n    React.useEffect(() => {\n        const ownerDocument = getOwnerDocument();\n        const listener = () => {\n            setFullscreen(getFullscreenElement() === containerRef.current);\n        };\n        return cleanup(...[\"fullscreenchange\", \"webkitfullscreenchange\", \"mozfullscreenchange\", \"MSFullscreenChange\"].map((event) => {\n            ownerDocument.addEventListener(event, listener);\n            return () => ownerDocument.removeEventListener(event, listener);\n        }));\n    }, [getFullscreenElement, getOwnerDocument]);\n    const onEnterFullscreen = useEventCallback(() => { var _a; return (_a = on.enterFullscreen) === null || _a === void 0 ? void 0 : _a.call(on); });\n    const onExitFullscreen = useEventCallback(() => { var _a; return (_a = on.exitFullscreen) === null || _a === void 0 ? void 0 : _a.call(on); });\n    React.useEffect(() => {\n        if (fullscreen) {\n            wasFullscreen.current = true;\n        }\n        if (wasFullscreen.current) {\n            (fullscreen ? onEnterFullscreen : onExitFullscreen)();\n        }\n    }, [fullscreen, onEnterFullscreen, onExitFullscreen]);\n    const handleAutoFullscreen = useEventCallback(() => {\n        var _a;\n        (_a = (auto ? enter : null)) === null || _a === void 0 ? void 0 : _a();\n        return exit;\n    });\n    React.useEffect(handleAutoFullscreen, [handleAutoFullscreen]);\n    const context = React.useMemo(() => ({ fullscreen, disabled, enter, exit }), [fullscreen, disabled, enter, exit]);\n    React.useImperativeHandle(ref, () => context, [context]);\n    return (React.createElement(\"div\", { ref: containerRef, className: clsx(cssClass(PLUGIN_FULLSCREEN), cssClass(CLASS_FULLSIZE)) },\n        React.createElement(FullscreenContext.Provider, { value: context }, children)));\n}\n\nconst EnterFullscreenIcon = createIcon(\"EnterFullscreen\", React.createElement(\"path\", { d: \"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z\" }));\nconst ExitFullscreenIcon = createIcon(\"ExitFullscreen\", React.createElement(\"path\", { d: \"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z\" }));\nfunction FullscreenButton() {\n    var _a;\n    const { fullscreen, disabled, enter, exit } = useFullscreen();\n    const { render } = useLightboxProps();\n    if (disabled)\n        return null;\n    if (render.buttonFullscreen) {\n        return React.createElement(React.Fragment, null, (_a = render.buttonFullscreen) === null || _a === void 0 ? void 0 : _a.call(render, { fullscreen, disabled, enter, exit }));\n    }\n    return (React.createElement(IconButton, { disabled: disabled, label: fullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\", icon: fullscreen ? ExitFullscreenIcon : EnterFullscreenIcon, renderIcon: fullscreen ? render.iconExitFullscreen : render.iconEnterFullscreen, onClick: fullscreen ? exit : enter }));\n}\n\nfunction Fullscreen({ augment, contains, addParent }) {\n    augment(({ fullscreen, toolbar, ...restProps }) => ({\n        toolbar: addToolbarButton(toolbar, PLUGIN_FULLSCREEN, React.createElement(FullscreenButton, null)),\n        fullscreen: resolveFullscreenProps(fullscreen),\n        ...restProps,\n    }));\n    addParent(contains(PLUGIN_THUMBNAILS) ? PLUGIN_THUMBNAILS : MODULE_CONTROLLER, createModule(PLUGIN_FULLSCREEN, FullscreenContextProvider));\n}\n\nexport { Fullscreen as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAIvB,IAAM,yBAAyB;AAAA,EAC3B,MAAM;AAAA,EACN,KAAK;AACT;AACA,IAAM,yBAAyB,CAAC,gBAAgB;AAAA,EAC5C,GAAG;AAAA,EACH,GAAG;AACP;AAEA,IAAM,oBAA0B,oBAAc,IAAI;AAClD,IAAM,gBAAgB,eAAe,iBAAiB,qBAAqB,iBAAiB;AAC5F,SAAS,0BAA0B,EAAE,YAAY,iBAAiB,IAAI,SAAS,GAAG;AAC9E,QAAM,EAAE,MAAM,IAAI,IAAI,uBAAuB,eAAe;AAC5D,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS;AAC/C,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,KAAK;AACxD,QAAM,gBAAsB,aAAO,KAAK;AACxC,QAAM,EAAE,iBAAiB,IAAI,mBAAmB;AAChD,kBAAgB,MAAM;AAClB,QAAI,IAAI,IAAI,IAAI;AAChB,UAAM,gBAAgB,iBAAiB;AACvC,gBAAY,GAAG,MAAM,MAAM,MAAM,KAAK,cAAc,uBAAuB,QAAQ,OAAO,SAAS,KAAK,cAAc,6BAA6B,QAAQ,OAAO,SAAS,KAAK,cAAc,0BAA0B,QAAQ,OAAO,SAAS,KAAK,cAAc,yBAAyB,QAAQ,OAAO,SAAS,KAAK,MAAM;AAAA,EACnU,GAAG,CAAC,gBAAgB,CAAC;AACrB,QAAM,uBAA6B,kBAAY,MAAM;AACjD,QAAI;AACJ,UAAM,gBAAgB,iBAAiB;AACvC,UAAM,oBAAoB,cAAc,qBACpC,cAAc,2BACd,cAAc,wBACd,cAAc;AAClB,aAAS,KAAK,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,sBAAsB;AAAA,EACpL,GAAG,CAAC,gBAAgB,CAAC;AACrB,QAAM,QAAc,kBAAY,MAAM;AAClC,UAAM,YAAY,aAAa;AAC/B,QAAI;AACA,UAAI,UAAU,mBAAmB;AAC7B,kBAAU,kBAAkB,EAAE,MAAM,MAAM;AAAA,QAAE,CAAC;AAAA,MACjD,WACS,UAAU,yBAAyB;AACxC,kBAAU,wBAAwB;AAAA,MACtC,WACS,UAAU,sBAAsB;AACrC,kBAAU,qBAAqB;AAAA,MACnC,WACS,UAAU,qBAAqB;AACpC,kBAAU,oBAAoB;AAAA,MAClC;AAAA,IACJ,SACO,GAAG;AAAA,IACV;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,OAAa,kBAAY,MAAM;AACjC,QAAI,CAAC,qBAAqB;AACtB;AACJ,UAAM,gBAAgB,iBAAiB;AACvC,QAAI;AACA,UAAI,cAAc,gBAAgB;AAC9B,sBAAc,eAAe,EAAE,MAAM,MAAM;AAAA,QAAE,CAAC;AAAA,MAClD,WACS,cAAc,sBAAsB;AACzC,sBAAc,qBAAqB;AAAA,MACvC,WACS,cAAc,qBAAqB;AACxC,sBAAc,oBAAoB;AAAA,MACtC,WACS,cAAc,kBAAkB;AACrC,sBAAc,iBAAiB;AAAA,MACnC;AAAA,IACJ,SACO,GAAG;AAAA,IACV;AAAA,EACJ,GAAG,CAAC,sBAAsB,gBAAgB,CAAC;AAC3C,EAAM,gBAAU,MAAM;AAClB,UAAM,gBAAgB,iBAAiB;AACvC,UAAM,WAAW,MAAM;AACnB,oBAAc,qBAAqB,MAAM,aAAa,OAAO;AAAA,IACjE;AACA,WAAO,QAAQ,GAAG,CAAC,oBAAoB,0BAA0B,uBAAuB,oBAAoB,EAAE,IAAI,CAAC,UAAU;AACzH,oBAAc,iBAAiB,OAAO,QAAQ;AAC9C,aAAO,MAAM,cAAc,oBAAoB,OAAO,QAAQ;AAAA,IAClE,CAAC,CAAC;AAAA,EACN,GAAG,CAAC,sBAAsB,gBAAgB,CAAC;AAC3C,QAAM,oBAAoB,iBAAiB,MAAM;AAAE,QAAI;AAAI,YAAQ,KAAK,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,EAAG,CAAC;AAC/I,QAAM,mBAAmB,iBAAiB,MAAM;AAAE,QAAI;AAAI,YAAQ,KAAK,GAAG,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,EAAG,CAAC;AAC7I,EAAM,gBAAU,MAAM;AAClB,QAAI,YAAY;AACZ,oBAAc,UAAU;AAAA,IAC5B;AACA,QAAI,cAAc,SAAS;AACvB,OAAC,aAAa,oBAAoB,kBAAkB;AAAA,IACxD;AAAA,EACJ,GAAG,CAAC,YAAY,mBAAmB,gBAAgB,CAAC;AACpD,QAAM,uBAAuB,iBAAiB,MAAM;AAChD,QAAI;AACJ,KAAC,KAAM,OAAO,QAAQ,UAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AACrE,WAAO;AAAA,EACX,CAAC;AACD,EAAM,gBAAU,sBAAsB,CAAC,oBAAoB,CAAC;AAC5D,QAAM,UAAgB,cAAQ,OAAO,EAAE,YAAY,UAAU,OAAO,KAAK,IAAI,CAAC,YAAY,UAAU,OAAO,IAAI,CAAC;AAChH,EAAM,0BAAoB,KAAK,MAAM,SAAS,CAAC,OAAO,CAAC;AACvD,SAAc;AAAA,IAAc;AAAA,IAAO,EAAE,KAAK,cAAc,WAAW,KAAK,SAAS,iBAAiB,GAAG,SAAS,cAAc,CAAC,EAAE;AAAA,IACrH,oBAAc,kBAAkB,UAAU,EAAE,OAAO,QAAQ,GAAG,QAAQ;AAAA,EAAC;AACrF;AAEA,IAAM,sBAAsB,WAAW,mBAAyB,oBAAc,QAAQ,EAAE,GAAG,iFAAiF,CAAC,CAAC;AAC9K,IAAM,qBAAqB,WAAW,kBAAwB,oBAAc,QAAQ,EAAE,GAAG,gFAAgF,CAAC,CAAC;AAC3K,SAAS,mBAAmB;AACxB,MAAI;AACJ,QAAM,EAAE,YAAY,UAAU,OAAO,KAAK,IAAI,cAAc;AAC5D,QAAM,EAAE,OAAO,IAAI,iBAAiB;AACpC,MAAI;AACA,WAAO;AACX,MAAI,OAAO,kBAAkB;AACzB,WAAa,oBAAoB,gBAAU,OAAO,KAAK,OAAO,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,EAAE,YAAY,UAAU,OAAO,KAAK,CAAC,CAAC;AAAA,EAC/K;AACA,SAAc,oBAAc,YAAY,EAAE,UAAoB,OAAO,aAAa,oBAAoB,oBAAoB,MAAM,aAAa,qBAAqB,qBAAqB,YAAY,aAAa,OAAO,qBAAqB,OAAO,qBAAqB,SAAS,aAAa,OAAO,MAAM,CAAC;AAChT;AAEA,SAAS,WAAW,EAAE,SAAS,UAAU,UAAU,GAAG;AAClD,UAAQ,CAAC,EAAE,YAAY,SAAS,GAAG,UAAU,OAAO;AAAA,IAChD,SAAS,iBAAiB,SAAS,mBAAyB,oBAAc,kBAAkB,IAAI,CAAC;AAAA,IACjG,YAAY,uBAAuB,UAAU;AAAA,IAC7C,GAAG;AAAA,EACP,EAAE;AACF,YAAU,SAAS,iBAAiB,IAAI,oBAAoB,mBAAmB,aAAa,mBAAmB,yBAAyB,CAAC;AAC7I;", "names": []}