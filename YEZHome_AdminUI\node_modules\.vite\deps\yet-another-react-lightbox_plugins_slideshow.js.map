{"version": 3, "sources": ["../../yet-another-react-lightbox/dist/plugins/slideshow/index.js"], "sourcesContent": ["import * as React from 'react';\nimport { makeUseContext, useLightboxState, useTimeouts, useEvents, useC<PERSON>roller, useA11yContext, useEventCallback, cleanup, createIcon, useLightboxProps, useLoseFocus, IconButton, addTool<PERSON><PERSON>utton, createModule } from '../../index.js';\nimport { SLIDE_STATUS_LOADING, SLIDE_STATUS_PLAYING, ACTIVE_SLIDE_LOADING, ACTIVE_SLIDE_PLAYING, ACTIVE_SLIDE_ERROR, SLIDE_STATUS_ERROR, ACTIVE_SLIDE_COMPLETE, SLIDE_STATUS_COMPLETE, PLUGIN_SLIDESHOW } from '../../types.js';\n\nconst defaultSlideshowProps = {\n    autoplay: false,\n    delay: 3000,\n    ref: null,\n};\nconst resolveSlideshowProps = (slideshow) => ({\n    ...defaultSlideshowProps,\n    ...slideshow,\n});\n\nconst SlideshowContext = React.createContext(null);\nconst useSlideshow = makeUseContext(\"useSlideshow\", \"SlideshowContext\", SlideshowContext);\nfunction SlideshowContextProvider({ slideshow, carousel: { finite }, on, children }) {\n    const { autoplay, delay, ref } = resolveSlideshowProps(slideshow);\n    const wasPlaying = React.useRef(autoplay);\n    const [playing, setPlaying] = React.useState(autoplay);\n    const scheduler = React.useRef(undefined);\n    const slideStatus = React.useRef(undefined);\n    const { slides, currentIndex } = useLightboxState();\n    const { setTimeout, clearTimeout } = useTimeouts();\n    const { subscribe } = useEvents();\n    const { next } = useController();\n    const { setAutoPlaying } = useA11yContext();\n    React.useEffect(() => setAutoPlaying(playing), [playing, setAutoPlaying]);\n    const disabled = slides.length === 0 || (finite && currentIndex === slides.length - 1);\n    const play = React.useCallback(() => {\n        if (!playing && !disabled) {\n            setPlaying(true);\n        }\n    }, [playing, disabled]);\n    const pause = React.useCallback(() => {\n        if (playing) {\n            setPlaying(false);\n        }\n    }, [playing]);\n    const cancelScheduler = React.useCallback(() => {\n        clearTimeout(scheduler.current);\n        scheduler.current = undefined;\n    }, [clearTimeout]);\n    const scheduleNextSlide = useEventCallback(() => {\n        cancelScheduler();\n        if (!playing ||\n            disabled ||\n            slideStatus.current === SLIDE_STATUS_LOADING ||\n            slideStatus.current === SLIDE_STATUS_PLAYING) {\n            return;\n        }\n        scheduler.current = setTimeout(() => {\n            if (playing) {\n                slideStatus.current = undefined;\n                next();\n            }\n        }, delay);\n    });\n    React.useEffect(scheduleNextSlide, [currentIndex, playing, scheduleNextSlide]);\n    React.useEffect(() => {\n        if (playing && disabled) {\n            setPlaying(false);\n        }\n    }, [currentIndex, playing, disabled]);\n    const onSlideshowStart = useEventCallback(() => { var _a; return (_a = on.slideshowStart) === null || _a === void 0 ? void 0 : _a.call(on); });\n    const onSlideshowStop = useEventCallback(() => { var _a; return (_a = on.slideshowStop) === null || _a === void 0 ? void 0 : _a.call(on); });\n    React.useEffect(() => {\n        if (playing) {\n            onSlideshowStart();\n        }\n        else if (wasPlaying.current) {\n            onSlideshowStop();\n        }\n        wasPlaying.current = playing;\n    }, [playing, onSlideshowStart, onSlideshowStop]);\n    React.useEffect(() => cleanup(cancelScheduler, subscribe(ACTIVE_SLIDE_LOADING, () => {\n        slideStatus.current = SLIDE_STATUS_LOADING;\n        cancelScheduler();\n    }), subscribe(ACTIVE_SLIDE_PLAYING, () => {\n        slideStatus.current = SLIDE_STATUS_PLAYING;\n        cancelScheduler();\n    }), subscribe(ACTIVE_SLIDE_ERROR, () => {\n        slideStatus.current = SLIDE_STATUS_ERROR;\n        scheduleNextSlide();\n    }), subscribe(ACTIVE_SLIDE_COMPLETE, () => {\n        slideStatus.current = SLIDE_STATUS_COMPLETE;\n        scheduleNextSlide();\n    })), [subscribe, cancelScheduler, scheduleNextSlide]);\n    const context = React.useMemo(() => ({ playing, disabled, play, pause }), [playing, disabled, play, pause]);\n    React.useImperativeHandle(ref, () => context, [context]);\n    return React.createElement(SlideshowContext.Provider, { value: context }, children);\n}\n\nconst PlayIcon = createIcon(\"Play\", React.createElement(\"path\", { d: \"M8 5v14l11-7z\" }));\nconst PauseIcon = createIcon(\"Pause\", React.createElement(\"path\", { d: \"M6 19h4V5H6v14zm8-14v14h4V5h-4z\" }));\nfunction SlideshowButton() {\n    const { playing, disabled, play, pause } = useSlideshow();\n    const { render } = useLightboxProps();\n    const focusListeners = useLoseFocus(useController().focus, disabled);\n    if (render.buttonSlideshow) {\n        return React.createElement(React.Fragment, null, render.buttonSlideshow({ playing, disabled, play, pause }));\n    }\n    return (React.createElement(IconButton, { label: playing ? \"Pause\" : \"Play\", icon: playing ? PauseIcon : PlayIcon, renderIcon: playing ? render.iconSlideshowPause : render.iconSlideshowPlay, onClick: playing ? pause : play, disabled: disabled, ...focusListeners }));\n}\n\nfunction Slideshow({ augment, addModule }) {\n    augment(({ slideshow, toolbar, ...restProps }) => ({\n        toolbar: addToolbarButton(toolbar, PLUGIN_SLIDESHOW, React.createElement(SlideshowButton, null)),\n        slideshow: resolveSlideshowProps(slideshow),\n        ...restProps,\n    }));\n    addModule(createModule(PLUGIN_SLIDESHOW, SlideshowContextProvider));\n}\n\nexport { Slideshow as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAIvB,IAAM,wBAAwB;AAAA,EAC1B,UAAU;AAAA,EACV,OAAO;AAAA,EACP,KAAK;AACT;AACA,IAAM,wBAAwB,CAAC,eAAe;AAAA,EAC1C,GAAG;AAAA,EACH,GAAG;AACP;AAEA,IAAM,mBAAyB,oBAAc,IAAI;AACjD,IAAM,eAAe,eAAe,gBAAgB,oBAAoB,gBAAgB;AACxF,SAAS,yBAAyB,EAAE,WAAW,UAAU,EAAE,OAAO,GAAG,IAAI,SAAS,GAAG;AACjF,QAAM,EAAE,UAAU,OAAO,IAAI,IAAI,sBAAsB,SAAS;AAChE,QAAM,aAAmB,aAAO,QAAQ;AACxC,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,QAAQ;AACrD,QAAM,YAAkB,aAAO,MAAS;AACxC,QAAM,cAAoB,aAAO,MAAS;AAC1C,QAAM,EAAE,QAAQ,aAAa,IAAI,iBAAiB;AAClD,QAAM,EAAE,YAAY,aAAa,IAAI,YAAY;AACjD,QAAM,EAAE,UAAU,IAAI,UAAU;AAChC,QAAM,EAAE,KAAK,IAAI,cAAc;AAC/B,QAAM,EAAE,eAAe,IAAI,eAAe;AAC1C,EAAM,gBAAU,MAAM,eAAe,OAAO,GAAG,CAAC,SAAS,cAAc,CAAC;AACxE,QAAM,WAAW,OAAO,WAAW,KAAM,UAAU,iBAAiB,OAAO,SAAS;AACpF,QAAM,OAAa,kBAAY,MAAM;AACjC,QAAI,CAAC,WAAW,CAAC,UAAU;AACvB,iBAAW,IAAI;AAAA,IACnB;AAAA,EACJ,GAAG,CAAC,SAAS,QAAQ,CAAC;AACtB,QAAM,QAAc,kBAAY,MAAM;AAClC,QAAI,SAAS;AACT,iBAAW,KAAK;AAAA,IACpB;AAAA,EACJ,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,kBAAwB,kBAAY,MAAM;AAC5C,iBAAa,UAAU,OAAO;AAC9B,cAAU,UAAU;AAAA,EACxB,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,oBAAoB,iBAAiB,MAAM;AAC7C,oBAAgB;AAChB,QAAI,CAAC,WACD,YACA,YAAY,YAAY,wBACxB,YAAY,YAAY,sBAAsB;AAC9C;AAAA,IACJ;AACA,cAAU,UAAU,WAAW,MAAM;AACjC,UAAI,SAAS;AACT,oBAAY,UAAU;AACtB,aAAK;AAAA,MACT;AAAA,IACJ,GAAG,KAAK;AAAA,EACZ,CAAC;AACD,EAAM,gBAAU,mBAAmB,CAAC,cAAc,SAAS,iBAAiB,CAAC;AAC7E,EAAM,gBAAU,MAAM;AAClB,QAAI,WAAW,UAAU;AACrB,iBAAW,KAAK;AAAA,IACpB;AAAA,EACJ,GAAG,CAAC,cAAc,SAAS,QAAQ,CAAC;AACpC,QAAM,mBAAmB,iBAAiB,MAAM;AAAE,QAAI;AAAI,YAAQ,KAAK,GAAG,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,EAAG,CAAC;AAC7I,QAAM,kBAAkB,iBAAiB,MAAM;AAAE,QAAI;AAAI,YAAQ,KAAK,GAAG,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,EAAG,CAAC;AAC3I,EAAM,gBAAU,MAAM;AAClB,QAAI,SAAS;AACT,uBAAiB;AAAA,IACrB,WACS,WAAW,SAAS;AACzB,sBAAgB;AAAA,IACpB;AACA,eAAW,UAAU;AAAA,EACzB,GAAG,CAAC,SAAS,kBAAkB,eAAe,CAAC;AAC/C,EAAM,gBAAU,MAAM,QAAQ,iBAAiB,UAAU,sBAAsB,MAAM;AACjF,gBAAY,UAAU;AACtB,oBAAgB;AAAA,EACpB,CAAC,GAAG,UAAU,sBAAsB,MAAM;AACtC,gBAAY,UAAU;AACtB,oBAAgB;AAAA,EACpB,CAAC,GAAG,UAAU,oBAAoB,MAAM;AACpC,gBAAY,UAAU;AACtB,sBAAkB;AAAA,EACtB,CAAC,GAAG,UAAU,uBAAuB,MAAM;AACvC,gBAAY,UAAU;AACtB,sBAAkB;AAAA,EACtB,CAAC,CAAC,GAAG,CAAC,WAAW,iBAAiB,iBAAiB,CAAC;AACpD,QAAM,UAAgB,cAAQ,OAAO,EAAE,SAAS,UAAU,MAAM,MAAM,IAAI,CAAC,SAAS,UAAU,MAAM,KAAK,CAAC;AAC1G,EAAM,0BAAoB,KAAK,MAAM,SAAS,CAAC,OAAO,CAAC;AACvD,SAAa,oBAAc,iBAAiB,UAAU,EAAE,OAAO,QAAQ,GAAG,QAAQ;AACtF;AAEA,IAAM,WAAW,WAAW,QAAc,oBAAc,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC;AACvF,IAAM,YAAY,WAAW,SAAe,oBAAc,QAAQ,EAAE,GAAG,kCAAkC,CAAC,CAAC;AAC3G,SAAS,kBAAkB;AACvB,QAAM,EAAE,SAAS,UAAU,MAAM,MAAM,IAAI,aAAa;AACxD,QAAM,EAAE,OAAO,IAAI,iBAAiB;AACpC,QAAM,iBAAiB,aAAa,cAAc,EAAE,OAAO,QAAQ;AACnE,MAAI,OAAO,iBAAiB;AACxB,WAAa,oBAAoB,gBAAU,MAAM,OAAO,gBAAgB,EAAE,SAAS,UAAU,MAAM,MAAM,CAAC,CAAC;AAAA,EAC/G;AACA,SAAc,oBAAc,YAAY,EAAE,OAAO,UAAU,UAAU,QAAQ,MAAM,UAAU,YAAY,UAAU,YAAY,UAAU,OAAO,qBAAqB,OAAO,mBAAmB,SAAS,UAAU,QAAQ,MAAM,UAAoB,GAAG,eAAe,CAAC;AAC3Q;AAEA,SAAS,UAAU,EAAE,SAAS,UAAU,GAAG;AACvC,UAAQ,CAAC,EAAE,WAAW,SAAS,GAAG,UAAU,OAAO;AAAA,IAC/C,SAAS,iBAAiB,SAAS,kBAAwB,oBAAc,iBAAiB,IAAI,CAAC;AAAA,IAC/F,WAAW,sBAAsB,SAAS;AAAA,IAC1C,GAAG;AAAA,EACP,EAAE;AACF,YAAU,aAAa,kBAAkB,wBAAwB,CAAC;AACtE;", "names": []}