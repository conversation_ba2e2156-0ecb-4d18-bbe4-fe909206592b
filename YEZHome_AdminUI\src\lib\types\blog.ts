export interface BlogPost {
  id: string;
  authorID: string;
  authorName: string;
  title: string;
  slug: string;
  content: string;
  featuredImage?: string;
  tags?: string;
  status: string;
  publishedAt?: string;
  blogComments?: BlogComment[];
}

export interface BlogComment {
  id: string;
  commentText: string;
  userId: string;
  postId: string;
}

export interface CreateBlogPost {
  authorID: string;
  title: string;
  content: string;
  featuredImage?: string;
  tags?: string;
  status?: string;
  publishedAt?: string | null;
}

export interface PagedBlogPosts {
  items: BlogPost[];
  totalCount: number;
  pageCount: number;
  currentPage: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export enum BlogStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  ARCHIVED = "archived"
} 