import axiosInstance from './axios-config';
import type { UserDto, UserDtoPagedResultDto, CreateAdminUserDto, UpdateUserStatusDto } from '@/lib/types/user';
import type { AdminRoleDto, UpdateUserRoleDto } from '@/lib/types/role';

interface EmployeeSearchParams {
  PageNumber?: number;
  PageSize?: number;
  Email?: string;
  Name?: string;
  Phone?: string;
  UserType?: string;
  IsActive?: boolean;
  SortColumn?: string;
  SortDescending?: boolean;
}

interface CustomerSearchParams {
  PageNumber?: number;
  PageSize?: number;
  Email?: string;
  Name?: string;
  Phone?: string;
  UserType?: string;
  IsActive?: boolean;
  SortColumn?: string;
  SortDescending?: boolean;
}

interface UserDashboardDto {
  userInfo: {
    id: string;
    fullName: string;
    email: string;
    phone: string;
    userType: string;
    memberRank: string;
    lastLogin: string;
    createdAt: string;
    roles: string[];
  };
  walletInfo: {
    balance: number;
    totalSpent: number;
    totalTransactions: number;
    lastMonthSpending: number;
  };
  propertyStats: {
    totalProperties: number;
    activeProperties: number;
    expiredProperties: number;
    draftProperties: number;
    favoriteProperties: number;
    totalViews: number;
    averageRating: number;
  };
  recentTransactions: any[]; // Can be refined if transaction structure is known
  memberRanking: {
    currentRank: string;
    nextRank: string;
    spendingToNextRank: number;
    minSpent: number;
    maxSpent: number;
    progressPercentage: number;
  };
}

const userService = {
  // Lấy danh sách nhân viên với bộ lọc
  getEmployees: async (params: EmployeeSearchParams): Promise<UserDtoPagedResultDto> => {
    const response = await axiosInstance.get('/User/employees', { params });
    return response.data;
  },

  // Lấy danh sách khách hàng với bộ lọc
  getCustomers: async (params: CustomerSearchParams): Promise<UserDtoPagedResultDto> => {
    const response = await axiosInstance.get('/User/customers', { params });
    return response.data;
  },

  // Lấy thông tin chi tiết của người dùng
  getUserById: async (id: string): Promise<UserDto> => {
    const response = await axiosInstance.get(`/User/${id}`);
    return response.data;
  },

  // Lấy thông tin dashboard của khách hàng
  getUserDashboard: async (userId: string): Promise<UserDashboardDto> => {
    const response = await axiosInstance.get(`/User/dashboard/${userId}`);
    return response.data;
  },

  // Lấy thông tin xuất hóa đơn của khách hàng
  getUserInvoiceInfo: async (userId: string): Promise<UserDto['invoiceInfo']> => {
    const response = await axiosInstance.get(`/User/${userId}/invoice-info`);
    return response.data;
  },

  // Thêm nhân viên mới
  createEmployee: async (data: CreateAdminUserDto): Promise<UserDto> => {
    const response = await axiosInstance.post('/User/employees', data);
    return response.data;
  },

  // Cập nhật trạng thái người dùng (kích hoạt/vô hiệu hóa)
  updateUserStatus: async (userId: string, data: UpdateUserStatusDto): Promise<void> => {
    await axiosInstance.put(`/User/${userId}/status`, data);
  },

  // Xóa người dùng (giả định API endpoint)
  deleteUser: async (userId: string): Promise<void> => {
    // Note: This is a placeholder. The actual API endpoint for deletion might be different
    // or might not exist in the current API documentation
    await axiosInstance.delete(`/User/${userId}`);
  },

  // Lấy danh sách vai trò
  getRoles: async (): Promise<AdminRoleDto[]> => {
    const response = await axiosInstance.get('/RolePermission/roles');
    return response.data;
  },

  // Cập nhật vai trò cho người dùng (chỉ một vai trò)
  updateUserRoles: async (data: UpdateUserRoleDto): Promise<void> => {
    // Ensure roleIds is always an array with a single item
    const roleIds = Array.isArray(data.roleIds) ? data.roleIds : [data.roleIds];
    await axiosInstance.put('/User/roles', {
      ...data,
      roleIds: roleIds
    });
  }
};

export default userService; 