# Kế hoạch triển khai tính năng Quản lý Bất Động Sản

Tài liệu này trình bày kế hoạch chi tiết để triển khai tính năng quản lý bất động sản cho trang quản trị.

## 1. Tổng quan tính năng

Mục tiêu là cho phép quản trị viên thực hiện các thao tác quản lý bất động sản trong hệ thống:

- **Liệt kê Bất động sản:** Xem danh sách phân trang của tất cả bất động sản với khả năng sắp xếp, lọc theo loại BĐS, loại tin và trạng thái.
- **Xem chi tiết Bất động sản:** Xem thông tin chi tiết của một bất động sản, bao gồ<PERSON> thông tin c<PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, vị trí trên bản đồ và lịch sử thay đổi trạng thái.
- **Phê duyệt/Từ chối Bất động sản:** Thay đổi trạng thái của bất động sản (phê duyệt hoặc từ chối với lý do).
- **Xóa Bất động sản:** Xóa một hoặc nhiều bất động sản khỏi hệ thống.

## 2. Các Endpoints API

Tính năng sẽ sử dụng các endpoints API sau:

- `GET /api/Property/count-by-status`: Lấy thống kê số lượng bất động sản theo trạng thái.
- `GET /api/Property/search`: Tìm kiếm bất động sản với các bộ lọc.
- `GET /api/Property/search/count`: Đếm số lượng bất động sản thỏa mãn điều kiện tìm kiếm.
- `GET /api/Property/{id}`: Lấy thông tin chi tiết của một bất động sản.
- `GET /api/Property/{id}/history`: Lấy lịch sử thay đổi trạng thái của bất động sản.
- `PUT /api/Property/{propertyId}/status`: Cập nhật trạng thái của bất động sản.
- `DELETE /api/Property/{id}`: Xóa một bất động sản.
- `DELETE /api/Property/bulk`: Xóa nhiều bất động sản.
- `PUT /api/Property/bulk/status`: Cập nhật trạng thái của nhiều bất động sản.

## 3. Chi tiết triển khai

### 3.1. Cấu trúc tệp

Các tệp sẽ được tổ chức theo cấu trúc sau:

- **Types:** `src/lib/types/property.ts` - Chứa các interface TypeScript cho PropertyDto, PropertySearchParams, v.v.
- **Features:**
  - `src/features/property-management/` - Thư mục chính cho tính năng quản lý bất động sản
    - `components/` - Các components riêng cho tính năng bất động sản
      - `property-table.tsx`: Bảng dữ liệu hiển thị danh sách bất động sản
      - `property-filter.tsx`: Component bộ lọc tìm kiếm bất động sản
      - `property-detail.tsx`: Component hiển thị chi tiết bất động sản
      - `property-image-gallery.tsx`: Gallery hiển thị hình ảnh bất động sản
      - `detail-map.tsx`: Component hiển thị bản đồ vị trí bất động sản
    - `hooks/use-property.ts` - Custom hooks cho tính năng bất động sản (nếu cần)
- **Services:** `src/services/property-service.ts` - Các hàm để tương tác với API của bất động sản
- **Routes/Pages:**
  - `src/routes/_authenticated/property/index.tsx`: Trang chính liệt kê danh sách bất động sản
  - `src/routes/_authenticated/property/$propertyId.tsx`: Trang xem chi tiết bất động sản

### 3.2. Định nghĩa Types (`src/lib/types/property.ts`)

Các interface chính bao gồm:

```typescript
// Thống kê số lượng bất động sản theo trạng thái
export interface PropertyCountStats {
  totalProperties: number;
  propertiesByStatus: Record<string, number>;
}

// Thông tin bất động sản
export interface PropertyDto {
  id: string;
  name: string;
  slug?: string;
  propertyType?: PropertyType | string;
  postType?: PostType | string;
  address?: string;
  area?: number;
  price: number;
  // ... các trường khác
  propertyMedia?: PropertyMediaDto[];
  status?: PropertyStatus | string;
  createdAt: string;
  // ... và các trường khác
}

// Tham số tìm kiếm bất động sản
export interface PropertySearchParams {
  postType?: string[];
  propertyType?: string[];
  status?: string[];
  page?: number;
  pageSize?: number;
}

// Kết quả phân trang
export interface PagedResultDto<T> {
  items: T[];
  totalCount: number;
  pageCount: number;
  currentPage: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

// Cập nhật trạng thái
export interface UpdateStatusDto {
  status: string;
  comment?: string;
}

// Lịch sử bất động sản
export interface PropertyHistoryDto {
  status: PropertyStatus | string;
  changedAt: string;
  comment: string;
  propertyId: string;
}
```

### 3.3. API Service (`src/services/property-service.ts`)

Service này sẽ đóng gói tất cả các lời gọi API liên quan đến bất động sản:

```typescript
// src/services/property-service.ts
import axiosInstance from './axios-config';
import type {
  PropertyCountStats,
  PropertyDto,
  PagedResultDto,
  PropertySearchParams,
  UpdateStatusDto,
  BulkPropertyIdsDto,
  BulkUpdateStatusDto,
  PropertyHistoryDto
} from '@/lib/types/property';

class PropertyService {
  // Lấy thống kê số lượng bất động sản theo trạng thái
  async getPropertyCountByStatus(): Promise<PropertyCountStats> {
    const response = await axiosInstance.get<PropertyCountStats>('Property/count-by-status');
    return response.data;
  }

  // Tìm kiếm bất động sản với các bộ lọc
  async searchProperties(params: PropertySearchParams): Promise<PagedResultDto<PropertyDto>> {
    // Xử lý tham số tìm kiếm
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString());
    
    // Xử lý các tham số mảng
    if (params.postType && params.postType.length > 0) {
      params.postType.forEach(type => {
        searchParams.append('postType', type);
      });
    }
    
    // ... xử lý các tham số khác
    
    const response = await axiosInstance.get<PagedResultDto<PropertyDto>>(`Property/search?${searchParams.toString()}`);
    return response.data;
  }

  // Lấy thông tin chi tiết bất động sản
  async getPropertyById(id: string): Promise<PropertyDto> {
    const response = await axiosInstance.get<PropertyDto>(`Property/${id}`);
    return response.data;
  }

  // Lấy lịch sử bất động sản
  async getPropertyHistoryById(id: string): Promise<PropertyHistoryDto[]> {
    const response = await axiosInstance.get<PropertyHistoryDto[]>(`Property/${id}/history`);
    return response.data;
  }

  // Cập nhật trạng thái bất động sản
  async updatePropertyStatus(propertyId: string, data: UpdateStatusDto): Promise<void> {
    await axiosInstance.put(`Property/${propertyId}/status`, data);
  }

  // ... các phương thức khác
}

export default new PropertyService();
```

### 3.4. Routing và UI

#### Trang danh sách bất động sản (`src/routes/_authenticated/property/index.tsx`)

- Sử dụng `@tanstack/react-table` cho bảng dữ liệu.
- Bảng sử dụng ShadcnUI table trong folder `src/components/ui/table.tsx` để render UI
- Sử dụng `useQuery` của `@tanstack/react-query` để lấy dữ liệu
- Triển khai bộ lọc theo loại BĐS, loại tin và trạng thái
- Phân trang và sắp xếp dữ liệu
- Các hành động: Xem chi tiết, Chỉnh sửa, Xóa

#### Trang chi tiết bất động sản (`src/routes/_authenticated/property/$propertyId.tsx`)

- Hiển thị thông tin chi tiết của bất động sản
- Gallery hình ảnh
- Hiển thị vị trí trên bản đồ sử dụng Goong Maps
- Lịch sử thay đổi trạng thái
- Các hành động: Phê duyệt, Từ chối (với lý do)

#### Components chính

1. **PropertyImageGallery (`src/features/property-management/components/property-image-gallery.tsx`)**
   - Hiển thị gallery hình ảnh với lightbox
   - Hỗ trợ xem toàn màn hình, slideshow và zoom

2. **DetailMap (`src/features/property-management/components/detail-map.tsx`)**
   - Hiển thị bản đồ với marker vị trí bất động sản
   - Sử dụng Goong Maps API

### 3.5. Quản lý Trạng thái

- `@tanstack/query` xử lý trạng thái máy chủ (caching, refetching)
- `@tanstack/react-router` quản lý trạng thái URL cho phân trang và bộ lọc
- Sử dụng `useMutation` cho các thao tác cập nhật trạng thái và xóa

## 4. Các công việc

1. **Cài đặt:**
   - Tạo thư mục `src/features/property-management`
   - Di chuyển các components hiện có vào cấu trúc thư mục mới
   - Cập nhật các import paths trong code

2. **Chuyển đổi cấu trúc:**
   - Di chuyển `src/components/property/DetailMap.tsx` sang `src/features/property-management/components/detail-map.tsx`
   - Di chuyển `src/components/property/PropertyImageGallery.tsx` sang `src/features/property-management/components/property-image-gallery.tsx`
   - Giữ nguyên `src/services/property-service.ts` tại vị trí hiện tại

3. **Cập nhật các tham chiếu:**
   - Cập nhật tất cả các import trong các tệp routes để tham chiếu đến vị trí mới của các components
   - Đảm bảo không có lỗi tham chiếu sau khi di chuyển

4. **Kiểm thử:**
   - Kiểm tra tất cả các chức năng sau khi tái cấu trúc
   - Đảm bảo tất cả các tính năng vẫn hoạt động như mong đợi

## 5. Lưu ý

- Đảm bảo xác thực và phân quyền phù hợp
- Xử lý lỗi và hiển thị thông báo phù hợp
- Đảm bảo giao diện người dùng nhất quán với phần còn lại của ứng dụng
- Tối ưu hóa hiệu suất cho việc tải và hiển thị hình ảnh bất động sản 