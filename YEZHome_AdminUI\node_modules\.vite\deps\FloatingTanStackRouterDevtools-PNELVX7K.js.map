{"version": 3, "sources": ["../../@tanstack/router-devtools-core/src/logo.tsx", "../../@tanstack/router-devtools-core/src/FloatingTanStackRouterDevtools.tsx"], "sourcesContent": ["import { createUniqueId } from 'solid-js'\n\nexport function TanStackLogo() {\n  const id = createUniqueId()\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      enable-background=\"new 0 0 634 633\"\n      viewBox=\"0 0 634 633\"\n    >\n      <g transform=\"translate(1)\">\n        <linearGradient\n          id={`a-${id}`}\n          x1=\"-641.486\"\n          x2=\"-641.486\"\n          y1=\"856.648\"\n          y2=\"855.931\"\n          gradientTransform=\"matrix(633 0 0 -633 406377 542258)\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\"0\" stop-color=\"#6bdaff\"></stop>\n          <stop offset=\"0.319\" stop-color=\"#f9ffb5\"></stop>\n          <stop offset=\"0.706\" stop-color=\"#ffa770\"></stop>\n          <stop offset=\"1\" stop-color=\"#ff7373\"></stop>\n        </linearGradient>\n        <circle\n          cx=\"316.5\"\n          cy=\"316.5\"\n          r=\"316.5\"\n          fill={`url(#a-${id})`}\n          fill-rule=\"evenodd\"\n          clip-rule=\"evenodd\"\n        ></circle>\n        <defs>\n          <filter\n            id={`b-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"-137.5\"\n            y=\"412\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`c-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"-137.5\"\n          y=\"412\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#b-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"89.5\"\n          cy=\"610.5\"\n          fill=\"#015064\"\n          fill-rule=\"evenodd\"\n          stroke=\"#00CFE2\"\n          stroke-width=\"25\"\n          clip-rule=\"evenodd\"\n          mask={`url(#c-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`d-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"316.5\"\n            y=\"412\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`e-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"316.5\"\n          y=\"412\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#d-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"543.5\"\n          cy=\"610.5\"\n          fill=\"#015064\"\n          fill-rule=\"evenodd\"\n          stroke=\"#00CFE2\"\n          stroke-width=\"25\"\n          clip-rule=\"evenodd\"\n          mask={`url(#e-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`f-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"-137.5\"\n            y=\"450\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`g-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"-137.5\"\n          y=\"450\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#f-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"89.5\"\n          cy=\"648.5\"\n          fill=\"#015064\"\n          fill-rule=\"evenodd\"\n          stroke=\"#00A8B8\"\n          stroke-width=\"25\"\n          clip-rule=\"evenodd\"\n          mask={`url(#g-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`h-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"316.5\"\n            y=\"450\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`i-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"316.5\"\n          y=\"450\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#h-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"543.5\"\n          cy=\"648.5\"\n          fill=\"#015064\"\n          fill-rule=\"evenodd\"\n          stroke=\"#00A8B8\"\n          stroke-width=\"25\"\n          clip-rule=\"evenodd\"\n          mask={`url(#i-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`j-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"-137.5\"\n            y=\"486\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`k-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"-137.5\"\n          y=\"486\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#j-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"89.5\"\n          cy=\"684.5\"\n          fill=\"#015064\"\n          fill-rule=\"evenodd\"\n          stroke=\"#007782\"\n          stroke-width=\"25\"\n          clip-rule=\"evenodd\"\n          mask={`url(#k-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`l-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"316.5\"\n            y=\"486\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`m-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"316.5\"\n          y=\"486\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#l-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"543.5\"\n          cy=\"684.5\"\n          fill=\"#015064\"\n          fill-rule=\"evenodd\"\n          stroke=\"#007782\"\n          stroke-width=\"25\"\n          clip-rule=\"evenodd\"\n          mask={`url(#m-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`n-${id}`}\n            width=\"176.9\"\n            height=\"129.3\"\n            x=\"272.2\"\n            y=\"308\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`o-${id}`}\n          width=\"176.9\"\n          height=\"129.3\"\n          x=\"272.2\"\n          y=\"308\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#n-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <g mask={`url(#o-${id})`}>\n          <path\n            fill=\"none\"\n            stroke=\"#000\"\n            stroke-linecap=\"round\"\n            stroke-linejoin=\"bevel\"\n            stroke-width=\"11\"\n            d=\"M436 403.2l-5 28.6m-140-90.3l-10.9 62m52.8-19.4l-4.3 27.1\"\n          ></path>\n          <linearGradient\n            id={`p-${id}`}\n            x1=\"-645.656\"\n            x2=\"-646.499\"\n            y1=\"854.878\"\n            y2=\"854.788\"\n            gradientTransform=\"matrix(-184.159 -32.4722 11.4608 -64.9973 -128419.844 34938.836)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#ee2700\"></stop>\n            <stop offset=\"1\" stop-color=\"#ff008e\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#p-${id})`}\n            fill-rule=\"evenodd\"\n            d=\"M344.1 363l97.7 17.2c5.8 2.1 8.2 6.2 7.1 12.1-1 5.9-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1.8-12.8 3.7-3.7 8.3-4.4 13.7-2.1l55.2 53.6z\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <path\n            fill=\"#D8D8D8\"\n            fill-rule=\"evenodd\"\n            stroke=\"#FFF\"\n            stroke-linecap=\"round\"\n            stroke-linejoin=\"bevel\"\n            stroke-width=\"7\"\n            d=\"M428.3 384.5l.9-6.5m-33.9 1.5l.9-6.5m-34 .5l.9-6.1m-38.9-16.1l4.2-3.9m-25.2-16.1l4.2-3.9\"\n            clip-rule=\"evenodd\"\n          ></path>\n        </g>\n        <defs>\n          <filter\n            id={`q-${id}`}\n            width=\"280.6\"\n            height=\"317.4\"\n            x=\"73.2\"\n            y=\"113.9\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`r-${id}`}\n          width=\"280.6\"\n          height=\"317.4\"\n          x=\"73.2\"\n          y=\"113.9\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#q-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <g mask={`url(#r-${id})`}>\n          <linearGradient\n            id={`s-${id}`}\n            x1=\"-646.8\"\n            x2=\"-646.8\"\n            y1=\"854.844\"\n            y2=\"853.844\"\n            gradientTransform=\"matrix(-100.1751 48.8587 -97.9753 -200.879 19124.773 203538.61)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#a17500\"></stop>\n            <stop offset=\"1\" stop-color=\"#5d2100\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#s-${id})`}\n            fill-rule=\"evenodd\"\n            d=\"M192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.2-2.9 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6-3.4-18.7-10.8-51.8-22.2-99.6l-25.3 4.6\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`t-${id}`}\n            x1=\"-635.467\"\n            x2=\"-635.467\"\n            y1=\"852.115\"\n            y2=\"851.115\"\n            gradientTransform=\"matrix(92.6873 4.8575 2.0257 -38.6535 57323.695 36176.047)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stop-color=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#t-${id})`}\n            fill-rule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            stroke-width=\"13\"\n            d=\"M195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`u-${id}`}\n            x1=\"-636.573\"\n            x2=\"-636.573\"\n            y1=\"855.444\"\n            y2=\"854.444\"\n            gradientTransform=\"matrix(109.9945 5.7646 6.3597 -121.3507 64719.133 107659.336)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stop-color=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#u-${id})`}\n            fill-rule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            stroke-width=\"13\"\n            d=\"M194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.3 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20 49.6-53.1 49.6-53.1z\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`v-${id}`}\n            x1=\"-632.145\"\n            x2=\"-632.145\"\n            y1=\"854.174\"\n            y2=\"853.174\"\n            gradientTransform=\"matrix(62.9558 3.2994 3.5021 -66.8246 37035.367 59284.227)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stop-color=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#v-${id})`}\n            fill-rule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            stroke-width=\"13\"\n            d=\"M195 183.9c-.8-21.9 6-38 20.6-48.2 14.6-10.2 29.8-15.3 45.5-15.3-6.1 21.4-14.5 35.8-25.2 43.4-10.7 7.5-24.4 14.2-40.9 20.1z\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`w-${id}`}\n            x1=\"-638.224\"\n            x2=\"-638.224\"\n            y1=\"853.801\"\n            y2=\"852.801\"\n            gradientTransform=\"matrix(152.4666 7.9904 3.0934 -59.0251 94939.86 55646.855)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stop-color=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#w-${id})`}\n            fill-rule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            stroke-width=\"13\"\n            d=\"M194.9 184.5c31.9-30 64.1-39.7 96.7-29 32.6 10.7 50.8 30.4 54.6 59.1-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`x-${id}`}\n            x1=\"-637.723\"\n            x2=\"-637.723\"\n            y1=\"855.103\"\n            y2=\"854.103\"\n            gradientTransform=\"matrix(136.467 7.1519 5.2165 -99.5377 82830.875 89859.578)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stop-color=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#x-${id})`}\n            fill-rule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            stroke-width=\"13\"\n            d=\"M194.9 184.5c35.8-7.6 65.6-.2 89.2 22 23.6 22.2 37.7 49 42.3 80.3-39.8-9.7-68.3-23.8-85.5-42.4-17.2-18.5-32.5-38.5-46-59.9z\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`y-${id}`}\n            x1=\"-631.79\"\n            x2=\"-631.79\"\n            y1=\"855.872\"\n            y2=\"854.872\"\n            gradientTransform=\"matrix(60.8683 3.19 8.7771 -167.4773 31110.818 145537.61)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stop-color=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stop-color=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#y-${id})`}\n            fill-rule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            stroke-width=\"13\"\n            d=\"M194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6-6.5 29.9-3.6 63.1 8.7 99.6 27.4-40.3 43.2-69.6 47.4-88 4.2-18.3 5.5-44.1 4-77.2z\"\n            clip-rule=\"evenodd\"\n          ></path>\n          <path\n            fill=\"none\"\n            stroke=\"#2F8A00\"\n            stroke-linecap=\"round\"\n            stroke-width=\"8\"\n            d=\"M196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4-5.7 18-9.4 33-11.1 45.1\"\n          ></path>\n          <path\n            fill=\"none\"\n            stroke=\"#2F8A00\"\n            stroke-linecap=\"round\"\n            stroke-width=\"8\"\n            d=\"M194.8 185.7c-24.4 1.7-43.8 9-58.1 21.8-14.3 12.8-24.7 25.4-31.3 37.8m99.1-68.9c29.7-6.7 52-8.4 67-5 15 3.4 26.9 8.7 35.8 15.9m-110.8-5.9c20.3 9.9 38.2 20.5 53.9 31.9 15.7 11.4 27.4 22.1 35.1 32\"\n          ></path>\n        </g>\n        <defs>\n          <filter\n            id={`z-${id}`}\n            width=\"532\"\n            height=\"633\"\n            x=\"50.5\"\n            y=\"399\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`A-${id}`}\n          width=\"532\"\n          height=\"633\"\n          x=\"50.5\"\n          y=\"399\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#z-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <linearGradient\n          id={`B-${id}`}\n          x1=\"-641.104\"\n          x2=\"-641.278\"\n          y1=\"856.577\"\n          y2=\"856.183\"\n          gradientTransform=\"matrix(532 0 0 -633 341484.5 542657)\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\"0\" stop-color=\"#fff400\"></stop>\n          <stop offset=\"1\" stop-color=\"#3c8700\"></stop>\n        </linearGradient>\n        <ellipse\n          cx=\"316.5\"\n          cy=\"715.5\"\n          fill={`url(#B-${id})`}\n          fill-rule=\"evenodd\"\n          clip-rule=\"evenodd\"\n          mask={`url(#A-${id})`}\n          rx=\"266\"\n          ry=\"316.5\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`C-${id}`}\n            width=\"288\"\n            height=\"283\"\n            x=\"391\"\n            y=\"-24\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`D-${id}`}\n          width=\"288\"\n          height=\"283\"\n          x=\"391\"\n          y=\"-24\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#C-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <g mask={`url(#D-${id})`}>\n          <g transform=\"translate(397 -24)\">\n            <linearGradient\n              id={`E-${id}`}\n              x1=\"-1036.672\"\n              x2=\"-1036.672\"\n              y1=\"880.018\"\n              y2=\"879.018\"\n              gradientTransform=\"matrix(227 0 0 -227 235493 199764)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffdf00\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff9d00\"></stop>\n            </linearGradient>\n            <circle\n              cx=\"168.5\"\n              cy=\"113.5\"\n              r=\"113.5\"\n              fill={`url(#E-${id})`}\n              fill-rule=\"evenodd\"\n              clip-rule=\"evenodd\"\n            ></circle>\n            <linearGradient\n              id={`F-${id}`}\n              x1=\"-1017.329\"\n              x2=\"-1018.602\"\n              y1=\"658.003\"\n              y2=\"657.998\"\n              gradientTransform=\"matrix(30 0 0 -1 30558 771)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#F-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M30 113H0\"\n            ></path>\n            <linearGradient\n              id={`G-${id}`}\n              x1=\"-1014.501\"\n              x2=\"-1015.774\"\n              y1=\"839.985\"\n              y2=\"839.935\"\n              gradientTransform=\"matrix(26.5 0 0 -5.5 26925 4696.5)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#G-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M33.5 79.5L7 74\"\n            ></path>\n            <linearGradient\n              id={`H-${id}`}\n              x1=\"-1016.59\"\n              x2=\"-1017.862\"\n              y1=\"852.671\"\n              y2=\"852.595\"\n              gradientTransform=\"matrix(29 0 0 -8 29523 6971)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#H-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M34 146l-29 8\"\n            ></path>\n            <linearGradient\n              id={`I-${id}`}\n              x1=\"-1011.984\"\n              x2=\"-1013.257\"\n              y1=\"863.523\"\n              y2=\"863.229\"\n              gradientTransform=\"matrix(24 0 0 -13 24339 11407)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#I-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M45 177l-24 13\"\n            ></path>\n            <linearGradient\n              id={`J-${id}`}\n              x1=\"-1006.673\"\n              x2=\"-1007.946\"\n              y1=\"869.279\"\n              y2=\"868.376\"\n              gradientTransform=\"matrix(20 0 0 -19 20205 16720)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#J-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M67 204l-20 19\"\n            ></path>\n            <linearGradient\n              id={`K-${id}`}\n              x1=\"-992.85\"\n              x2=\"-993.317\"\n              y1=\"871.258\"\n              y2=\"870.258\"\n              gradientTransform=\"matrix(13.8339 0 0 -22.8467 13825.796 20131.938)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#K-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M94.4 227l-13.8 22.8\"\n            ></path>\n            <linearGradient\n              id={`L-${id}`}\n              x1=\"-953.835\"\n              x2=\"-953.965\"\n              y1=\"871.9\"\n              y2=\"870.9\"\n              gradientTransform=\"matrix(7.5 0 0 -24.5 7278 21605)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#L-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M127.5 243.5L120 268\"\n            ></path>\n            <linearGradient\n              id={`M-${id}`}\n              x1=\"244.504\"\n              x2=\"244.496\"\n              y1=\"871.898\"\n              y2=\"870.898\"\n              gradientTransform=\"matrix(.5 0 0 -24.5 45.5 21614)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stop-color=\"#ffa400\"></stop>\n              <stop offset=\"1\" stop-color=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#M-${id})`}\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"bevel\"\n              stroke-width=\"12\"\n              d=\"M167.5 252.5l.5 24.5\"\n            ></path>\n          </g>\n        </g>\n      </g>\n    </svg>\n  )\n}\n", "import { clsx as cx } from 'clsx'\n\nimport { createEffect, createMemo, createSignal } from 'solid-js'\nimport { Dynamic } from 'solid-js/web'\n\nimport { DevtoolsOnCloseContext } from './context'\nimport { useIsMounted } from './utils'\nimport { BaseTanStackRouterDevtoolsPanel } from './BaseTanStackRouterDevtoolsPanel'\nimport useLocalStorage from './useLocalStorage'\nimport { TanStackLogo } from './logo'\nimport { useStyles } from './useStyles'\nimport type { Accessor, JSX } from 'solid-js'\nimport type { AnyRouter } from '@tanstack/router-core'\n\nexport interface FloatingDevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add class, style (merge and override default style), etc.\n   */\n  panelProps?: any & {\n    ref?: any\n  }\n  /**\n   * Use this to add props to the close button. For example, you can add class, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: any & {\n    ref?: any\n  }\n  /**\n   * Use this to add props to the toggle button. For example, you can add class, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: any & {\n    ref?: any\n  }\n  /**\n   * The position of the TanStack Router logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'footer'.\n   */\n  containerElement?: string | any\n  /**\n   * A boolean variable indicating if the \"lite\" version of the library is being used\n   */\n  router: Accessor<AnyRouter>\n  routerState: Accessor<any>\n  /**\n   * Use this to attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nexport function FloatingTanStackRouterDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'footer',\n  router,\n  routerState,\n  shadowDOMTarget,\n}: FloatingDevtoolsOptions): JSX.Element | null {\n  const [rootEl, setRootEl] = createSignal<HTMLDivElement>()\n\n  // eslint-disable-next-line prefer-const\n  let panelRef: HTMLDivElement | undefined = undefined\n\n  const [isOpen, setIsOpen] = useLocalStorage(\n    'tanstackRouterDevtoolsOpen',\n    initialIsOpen,\n  )\n\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage<number | null>(\n    'tanstackRouterDevtoolsHeight',\n    null,\n  )\n\n  const [isResolvedOpen, setIsResolvedOpen] = createSignal(false)\n  const [isResizing, setIsResizing] = createSignal(false)\n  const isMounted = useIsMounted()\n  const styles = useStyles()\n\n  const handleDragStart = (\n    panelElement: HTMLDivElement | undefined,\n    startEvent: any,\n  ) => {\n    if (startEvent.button !== 0) return // Only allow left click for drag\n\n    setIsResizing(true)\n\n    const dragInfo = {\n      originalHeight: panelElement?.getBoundingClientRect().height ?? 0,\n      pageY: startEvent.pageY,\n    }\n\n    const run = (moveEvent: MouseEvent) => {\n      const delta = dragInfo.pageY - moveEvent.pageY\n      const newHeight = dragInfo.originalHeight + delta\n\n      setDevtoolsHeight(newHeight)\n\n      if (newHeight < 70) {\n        setIsOpen(false)\n      } else {\n        setIsOpen(true)\n      }\n    }\n\n    const unsub = () => {\n      setIsResizing(false)\n      document.removeEventListener('mousemove', run)\n      document.removeEventListener('mouseUp', unsub)\n    }\n\n    document.addEventListener('mousemove', run)\n    document.addEventListener('mouseup', unsub)\n  }\n\n  const isButtonClosed = isOpen() ?? false\n\n  createEffect(() => {\n    setIsResolvedOpen(isOpen() ?? false)\n  })\n\n  createEffect(() => {\n    if (isResolvedOpen()) {\n      const previousValue = rootEl()?.parentElement?.style.paddingBottom\n\n      const run = () => {\n        const containerHeight = panelRef!.getBoundingClientRect().height\n        if (rootEl()?.parentElement) {\n          setRootEl((prev) => {\n            if (prev?.parentElement) {\n              prev.parentElement.style.paddingBottom = `${containerHeight}px`\n            }\n            return prev\n          })\n        }\n      }\n\n      run()\n\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run)\n\n        return () => {\n          window.removeEventListener('resize', run)\n          if (rootEl()?.parentElement && typeof previousValue === 'string') {\n            setRootEl((prev) => {\n              prev!.parentElement!.style.paddingBottom = previousValue\n              return prev\n            })\n          }\n        }\n      }\n    } else {\n      // Reset padding when devtools are closed\n      if (rootEl()?.parentElement) {\n        setRootEl((prev) => {\n          if (prev?.parentElement) {\n            prev.parentElement.removeAttribute('style')\n          }\n          return prev\n        })\n      }\n    }\n    return\n  })\n\n  createEffect(() => {\n    if (rootEl()) {\n      const el = rootEl()\n      const fontSize = getComputedStyle(el!).fontSize\n      el?.style.setProperty('--tsrd-font-size', fontSize)\n    }\n  })\n\n  const { style: panelStyle = {}, ...otherPanelProps } = panelProps as {\n    style?: Record<string, any>\n  }\n\n  const {\n    style: closeButtonStyle = {},\n    onClick: onCloseClick,\n    ...otherCloseButtonProps\n  } = closeButtonProps\n\n  const {\n    onClick: onToggleClick,\n    class: toggleButtonClassName,\n    ...otherToggleButtonProps\n  } = toggleButtonProps\n\n  // Do not render on the server\n  if (!isMounted()) return null\n\n  const resolvedHeight = createMemo(() => devtoolsHeight() ?? 500)\n\n  const basePanelClass = createMemo(() => {\n    return cx(\n      styles().devtoolsPanelContainer,\n      styles().devtoolsPanelContainerVisibility(!!isOpen()),\n      styles().devtoolsPanelContainerResizing(isResizing),\n      styles().devtoolsPanelContainerAnimation(\n        isResolvedOpen(),\n        resolvedHeight() + 16,\n      ),\n    )\n  })\n\n  const basePanelStyle = createMemo(() => {\n    return {\n      height: `${resolvedHeight()}px`,\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      ...(panelStyle || {}),\n    }\n  })\n\n  const buttonStyle = createMemo(() => {\n    return cx(\n      styles().mainCloseBtn,\n      styles().mainCloseBtnPosition(position),\n      styles().mainCloseBtnAnimation(!!isOpen()),\n      toggleButtonClassName,\n    )\n  })\n\n  return (\n    <Dynamic\n      component={Container}\n      ref={setRootEl}\n      class=\"TanStackRouterDevtools\"\n    >\n      <DevtoolsOnCloseContext.Provider\n        value={{\n          onCloseClick: onCloseClick ?? (() => {}),\n        }}\n      >\n        {/* {router() ? ( */}\n        <BaseTanStackRouterDevtoolsPanel\n          ref={panelRef as any}\n          {...otherPanelProps}\n          router={router}\n          routerState={routerState}\n          className={basePanelClass}\n          style={basePanelStyle}\n          isOpen={isResolvedOpen()}\n          setIsOpen={setIsOpen}\n          handleDragStart={(e) => handleDragStart(panelRef, e)}\n          shadowDOMTarget={shadowDOMTarget}\n        />\n        {/* ) : (\n          <p>No router</p>\n        )} */}\n      </DevtoolsOnCloseContext.Provider>\n\n      <button\n        type=\"button\"\n        {...otherToggleButtonProps}\n        aria-label=\"Open TanStack Router Devtools\"\n        onClick={(e) => {\n          setIsOpen(true)\n          onToggleClick && onToggleClick(e)\n        }}\n        class={buttonStyle()}\n      >\n        <div class={styles().mainCloseBtnIconContainer}>\n          <div class={styles().mainCloseBtnIconOuter}>\n            <TanStackLogo />\n          </div>\n          <div class={styles().mainCloseBtnIconInner}>\n            <TanStackLogo />\n          </div>\n        </div>\n        <div class={styles().mainCloseBtnDivider}>-</div>\n        <div class={styles().routerLogoCloseButton}>TanStack Router</div>\n      </button>\n    </Dynamic>\n  )\n}\n\nexport default FloatingTanStackRouterDevtools\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAASA,eAAe;AAC7B,QAAMC,KAAKC,eAAe;AAC1B,UAAA,MAAA;AAAA,QAAAC,OAAAC,OAAAC,GAAAA,QAAAF,KAAAG,YAAAC,QAAAF,MAAAC,YAAAE,QAAAD,MAAAE,aAAAC,QAAAF,MAAAC,aAAAE,QAAAD,MAAAJ,YAAAM,QAAAF,MAAAD,aAAAI,QAAAD,MAAAN,YAAAQ,QAAAF,MAAAH,aAAAM,QAAAD,MAAAL,aAAAO,QAAAD,MAAAT,YAAAW,SAAAF,MAAAN,aAAAS,SAAAD,OAAAX,YAAAa,SAAAF,OAAAR,aAAAW,SAAAD,OAAAV,aAAAY,SAAAD,OAAAd,YAAAgB,SAAAF,OAAAX,aAAAc,SAAAD,OAAAhB,YAAAkB,SAAAF,OAAAb,aAAAgB,SAAAD,OAAAf,aAAAiB,SAAAD,OAAAnB,YAAAqB,SAAAF,OAAAhB,aAAAmB,SAAAD,OAAArB,YAAAuB,SAAAF,OAAAlB,aAAAqB,SAAAD,OAAApB,aAAAsB,SAAAD,OAAAxB,YAAA0B,SAAAF,OAAArB,aAAAwB,SAAAD,OAAA1B,YAAA4B,SAAAF,OAAAvB,aAAA0B,SAAAD,OAAAzB,aAAA2B,SAAAD,OAAA7B,YAAA+B,SAAAF,OAAA1B,aAAA6B,SAAAD,OAAA/B,YAAAiC,SAAAF,OAAA5B,aAAA+B,SAAAD,OAAA9B,aAAAgC,SAAAD,OAAAlC,YAAAoC,SAAAF,OAAA/B,aAAAkC,SAAAD,OAAApC,YAAAsC,SAAAF,OAAAjC,aAAAoC,SAAAD,OAAAtC,YAAAwC,SAAAD,OAAApC,aAAAsC,SAAAD,OAAArC,aAAAuC,SAAAJ,OAAAnC,aAAAwC,SAAAD,OAAA1C,YAAA4C,SAAAF,OAAAvC,aAAA0C,SAAAD,OAAA5C,YAAA8C,SAAAF,OAAAzC,aAAA4C,SAAAD,OAAA9C,YAAAgD,SAAAD,OAAA5C,aAAA8C,SAAAD,OAAA7C,aAAA+C,SAAAD,OAAA9C,aAAAgD,SAAAD,OAAA/C,aAAAiD,SAAAD,OAAAhD,aAAAkD,SAAAD,OAAAjD,aAAAmD,SAAAD,OAAAlD,aAAAoD,SAAAD,OAAAnD,aAAAqD,SAAAD,OAAApD,aAAAsD,SAAAD,OAAArD,aAAAuD,SAAAD,OAAAtD,aAAAwD,SAAAD,OAAAvD,aAAAyD,SAAAD,OAAAxD,aAAA0D,SAAAf,OAAA3C,aAAA2D,SAAAD,OAAA7D,YAAA+D,SAAAF,OAAA1D,aAAA6D,SAAAD,OAAA/D,YAAAiE,SAAAF,OAAA5D,aAAA+D,SAAAD,OAAA9D,aAAAgE,SAAAD,OAAA/D,aAAAiE,SAAAD,OAAAnE,YAAAqE,SAAAF,OAAAhE,aAAAmE,SAAAD,OAAArE,YAAAuE,SAAAF,OAAAlE,aAAAqE,SAAAD,OAAAvE,YAAAyE,SAAAD,OAAAxE,YAAA0E,SAAAD,OAAAtE,aAAAwE,SAAAD,OAAAvE,aAAAyE,SAAAD,OAAAxE,aAAA0E,SAAAD,OAAAzE,aAAA2E,SAAAD,OAAA1E,aAAA4E,SAAAD,OAAA3E,aAAA6E,SAAAD,OAAA5E,aAAA8E,SAAAD,OAAA7E,aAAA+E,SAAAD,OAAA9E,aAAAgF,SAAAD,OAAA/E,aAAAiF,SAAAD,OAAAhF,aAAAkF,SAAAD,OAAAjF,aAAAmF,SAAAD,OAAAlF,aAAAoF,SAAAD,OAAAnF,aAAAqF,SAAAD,OAAApF,aAAAsF,SAAAD,OAAArF,aAAAuF,SAAAD,OAAAtF;AAAAwF,iBAAA1F,OAQY,MAAA,KAAKN,EAAE,EAAE;AAAAgG,iBAAAzF,OAiBP,QAAA,UAAUP,EAAE,GAAG;AAAAgG,iBAAAtF,OAMf,MAAA,KAAKV,EAAE,EAAE;AAAAgG,iBAAArF,OAWX,MAAA,KAAKX,EAAE,EAAE;AAAAgG,iBAAApF,OAOF,UAAA,UAAUZ,EAAE,GAAG;AAAAgG,iBAAAnF,OAmBpB,QAAA,UAAUb,EAAE,GAAG;AAAAgG,iBAAAjF,OAMf,MAAA,KAAKf,EAAE,EAAE;AAAAgG,iBAAAhF,QAWX,MAAA,KAAKhB,EAAE,EAAE;AAAAgG,iBAAA/E,QAOF,UAAA,UAAUjB,EAAE,GAAG;AAAAgG,iBAAA9E,QAmBpB,QAAA,UAAUlB,EAAE,GAAG;AAAAgG,iBAAA5E,QAMf,MAAA,KAAKpB,EAAE,EAAE;AAAAgG,iBAAA3E,QAWX,MAAA,KAAKrB,EAAE,EAAE;AAAAgG,iBAAA1E,QAOF,UAAA,UAAUtB,EAAE,GAAG;AAAAgG,iBAAAzE,QAmBpB,QAAA,UAAUvB,EAAE,GAAG;AAAAgG,iBAAAvE,QAMf,MAAA,KAAKzB,EAAE,EAAE;AAAAgG,iBAAAtE,QAWX,MAAA,KAAK1B,EAAE,EAAE;AAAAgG,iBAAArE,QAOF,UAAA,UAAU3B,EAAE,GAAG;AAAAgG,iBAAApE,QAmBpB,QAAA,UAAU5B,EAAE,GAAG;AAAAgG,iBAAAlE,QAMf,MAAA,KAAK9B,EAAE,EAAE;AAAAgG,iBAAAjE,QAWX,MAAA,KAAK/B,EAAE,EAAE;AAAAgG,iBAAAhE,QAOF,UAAA,UAAUhC,EAAE,GAAG;AAAAgG,iBAAA/D,QAmBpB,QAAA,UAAUjC,EAAE,GAAG;AAAAgG,iBAAA7D,QAMf,MAAA,KAAKnC,EAAE,EAAE;AAAAgG,iBAAA5D,QAWX,MAAA,KAAKpC,EAAE,EAAE;AAAAgG,iBAAA3D,QAOF,UAAA,UAAUrC,EAAE,GAAG;AAAAgG,iBAAA1D,QAmBpB,QAAA,UAAUtC,EAAE,GAAG;AAAAgG,iBAAAxD,QAMf,MAAA,KAAKxC,EAAE,EAAE;AAAAgG,iBAAAvD,QAWX,MAAA,KAAKzC,EAAE,EAAE;AAAAgG,iBAAAtD,QAOF,UAAA,UAAU1C,EAAE,GAAG;AAAAgG,iBAAArD,QAWnB,QAAA,UAAU3C,EAAE,GAAG;AAAAgG,iBAAAnD,QAUhB,MAAA,KAAK7C,EAAE,EAAE;AAAAgG,iBAAAlD,QAYP,QAAA,UAAU9C,EAAE,GAAG;AAAAgG,iBAAAhD,QAkBjB,MAAA,KAAKhD,EAAE,EAAE;AAAAgG,iBAAA/C,QAWX,MAAA,KAAKjD,EAAE,EAAE;AAAAgG,iBAAA9C,QAOF,UAAA,UAAUlD,EAAE,GAAG;AAAAgG,iBAAA7C,QAWnB,QAAA,UAAUnD,EAAE,GAAG;AAAAgG,iBAAA5C,QAEhB,MAAA,KAAKpD,EAAE,EAAE;AAAAgG,iBAAA3C,QAYP,QAAA,UAAUrD,EAAE,GAAG;AAAAgG,iBAAA1C,QAMjB,MAAA,KAAKtD,EAAE,EAAE;AAAAgG,iBAAAzC,QAYP,QAAA,UAAUvD,EAAE,GAAG;AAAAgG,iBAAAxC,QAQjB,MAAA,KAAKxD,EAAE,EAAE;AAAAgG,iBAAAvC,QAYP,QAAA,UAAUzD,EAAE,GAAG;AAAAgG,iBAAAtC,QAQjB,MAAA,KAAK1D,EAAE,EAAE;AAAAgG,iBAAArC,QAYP,QAAA,UAAU3D,EAAE,GAAG;AAAAgG,iBAAApC,QAQjB,MAAA,KAAK5D,EAAE,EAAE;AAAAgG,iBAAAnC,QAYP,QAAA,UAAU7D,EAAE,GAAG;AAAAgG,iBAAAlC,QAQjB,MAAA,KAAK9D,EAAE,EAAE;AAAAgG,iBAAAjC,QAYP,QAAA,UAAU/D,EAAE,GAAG;AAAAgG,iBAAAhC,QAQjB,MAAA,KAAKhE,EAAE,EAAE;AAAAgG,iBAAA/B,QAYP,QAAA,UAAUjE,EAAE,GAAG;AAAAgG,iBAAA7B,QAwBjB,MAAA,KAAKnE,EAAE,EAAE;AAAAgG,iBAAA5B,QAWX,MAAA,KAAKpE,EAAE,EAAE;AAAAgG,iBAAA3B,QAOF,UAAA,UAAUrE,EAAE,GAAG;AAAAgG,iBAAA1B,QAYtB,MAAA,KAAKtE,EAAE,EAAE;AAAAgG,iBAAAzB,QAcP,QAAA,UAAUvE,EAAE,GAAG;AAAAgG,iBAAAzB,QAGf,QAAA,UAAUvE,EAAE,GAAG;AAAAgG,iBAAAvB,QAMf,MAAA,KAAKzE,EAAE,EAAE;AAAAgG,iBAAAtB,QAWX,MAAA,KAAK1E,EAAE,EAAE;AAAAgG,iBAAArB,QAOF,UAAA,UAAU3E,EAAE,GAAG;AAAAgG,iBAAApB,QAWnB,QAAA,UAAU5E,EAAE,GAAG;AAAAgG,iBAAAlB,QAGd,MAAA,KAAK9E,EAAE,EAAE;AAAAgG,iBAAAjB,QAeP,QAAA,UAAU/E,EAAE,GAAG;AAAAgG,iBAAAhB,QAKjB,MAAA,KAAKhF,EAAE,EAAE;AAAAgG,iBAAAf,QAaL,UAAA,UAAUjF,EAAE,GAAG;AAAAgG,iBAAAd,QAOnB,MAAA,KAAKlF,EAAE,EAAE;AAAAgG,iBAAAb,QAaL,UAAA,UAAUnF,EAAE,GAAG;AAAAgG,iBAAAZ,QAOnB,MAAA,KAAKpF,EAAE,EAAE;AAAAgG,iBAAAX,QAaL,UAAA,UAAUrF,EAAE,GAAG;AAAAgG,iBAAAV,QAOnB,MAAA,KAAKtF,EAAE,EAAE;AAAAgG,iBAAAT,QAaL,UAAA,UAAUvF,EAAE,GAAG;AAAAgG,iBAAAR,QAOnB,MAAA,KAAKxF,EAAE,EAAE;AAAAgG,iBAAAP,QAaL,UAAA,UAAUzF,EAAE,GAAG;AAAAgG,iBAAAN,QAOnB,MAAA,KAAK1F,EAAE,EAAE;AAAAgG,iBAAAL,QAaL,UAAA,UAAU3F,EAAE,GAAG;AAAAgG,iBAAAJ,QAOnB,MAAA,KAAK5F,EAAE,EAAE;AAAAgG,iBAAAH,QAaL,UAAA,UAAU7F,EAAE,GAAG;AAAAgG,iBAAAF,QAOnB,MAAA,KAAK9F,EAAE,EAAE;AAAAgG,iBAAAD,QAaL,UAAA,UAAU/F,EAAE,GAAG;AAAAE,WAAAA;EAAAA,GAAA;AAWrC;;;;ACrvBO,SAAS+F,+BAA+B;EAC7CC;EACAC,aAAa,CAAC;EACdC,mBAAmB,CAAC;EACpBC,oBAAoB,CAAC;EACrBC,WAAW;EACXC,kBAAkBC,YAAY;EAC9BC;EACAC;EACAC;AACuB,GAAuB;AAC9C,QAAM,CAACC,QAAQC,SAAS,IAAIC,aAA6B;AAGzD,MAAIC,WAAuCC;AAE3C,QAAM,CAACC,QAAQC,SAAS,IAAIC,gBAC1B,8BACAjB,aACF;AAEA,QAAM,CAACkB,gBAAgBC,iBAAiB,IAAIF,gBAC1C,gCACA,IACF;AAEA,QAAM,CAACG,gBAAgBC,iBAAiB,IAAIT,aAAa,KAAK;AAC9D,QAAM,CAACU,YAAYC,aAAa,IAAIX,aAAa,KAAK;AACtD,QAAMY,YAAYC,aAAa;AAC/B,QAAMC,SAASC,UAAU;AAEnBC,QAAAA,kBAAkBA,CACtBC,cACAC,eACG;AACCA,QAAAA,WAAWC,WAAW,EAAG;AAE7BR,kBAAc,IAAI;AAElB,UAAMS,WAAW;MACfC,iBAAgBJ,gBAAAA,OAAAA,SAAAA,aAAcK,sBAAAA,EAAwBC,WAAU;MAChEC,OAAON,WAAWM;IACpB;AAEMC,UAAAA,MAAMA,CAACC,cAA0B;AAC/BC,YAAAA,QAAQP,SAASI,QAAQE,UAAUF;AACnCI,YAAAA,YAAYR,SAASC,iBAAiBM;AAE5CpB,wBAAkBqB,SAAS;AAE3B,UAAIA,YAAY,IAAI;AAClBxB,kBAAU,KAAK;MAAA,OACV;AACLA,kBAAU,IAAI;MAAA;IAElB;AAEA,UAAMyB,QAAQA,MAAM;AAClBlB,oBAAc,KAAK;AACVmB,eAAAA,oBAAoB,aAAaL,GAAG;AACpCK,eAAAA,oBAAoB,WAAWD,KAAK;IAC/C;AAESE,aAAAA,iBAAiB,aAAaN,GAAG;AACjCM,aAAAA,iBAAiB,WAAWF,KAAK;EAC5C;AAEuB1B,SAAAA,KAAY;AAEnC6B,eAAa,MAAM;AACC7B,sBAAAA,OAAAA,KAAY,KAAK;EAAA,CACpC;AAED6B,eAAa,MAAM;;AACjB,QAAIxB,eAAAA,GAAkB;AACpB,YAAMyB,iBAAgBnC,MAAAA,KAAAA,OAAAA,MAAAA,OAAAA,SAAAA,GAAUoC,kBAAVpC,OAAAA,SAAAA,GAAyBqC,MAAMC;AAErD,YAAMX,MAAMA,MAAM;;AACVY,cAAAA,kBAAkBpC,SAAUqB,sBAAAA,EAAwBC;AACtDzB,aAAAA,MAAAA,OAAAA,MAAAA,OAAAA,SAAAA,IAAUoC,eAAe;AAC3BnC,oBAAWuC,CAAS,SAAA;AAClB,gBAAIA,QAAAA,OAAAA,SAAAA,KAAMJ,eAAe;AACvBI,mBAAKJ,cAAcC,MAAMC,gBAAgB,GAAGC,eAAe;YAAA;AAEtDC,mBAAAA;UAAAA,CACR;QAAA;MAEL;AAEI,UAAA;AAEA,UAAA,OAAOC,WAAW,aAAa;AAC1BR,eAAAA,iBAAiB,UAAUN,GAAG;AAErC,eAAO,MAAM;;AACJK,iBAAAA,oBAAoB,UAAUL,GAAG;AACxC,gBAAI3B,MAAAA,OAAO,MAAPA,OAAAA,SAAAA,IAAUoC,kBAAiB,OAAOD,kBAAkB,UAAU;AAChElC,sBAAWuC,CAAS,SAAA;AACZJ,mBAAAA,cAAeC,MAAMC,gBAAgBH;AACpCK,qBAAAA;YAAAA,CACR;UAAA;QAEL;MAAA;IACF,OACK;AAEDxC,WAAAA,KAAAA,OAAAA,MAAAA,OAAAA,SAAAA,GAAUoC,eAAe;AAC3BnC,kBAAWuC,CAAS,SAAA;AAClB,cAAIA,QAAAA,OAAAA,SAAAA,KAAMJ,eAAe;AAClBA,iBAAAA,cAAcM,gBAAgB,OAAO;UAAA;AAErCF,iBAAAA;QAAAA,CACR;MAAA;IACH;AAEF;EAAA,CACD;AAEDN,eAAa,MAAM;AACjB,QAAIlC,OAAAA,GAAU;AACZ,YAAM2C,KAAK3C,OAAO;AACZ4C,YAAAA,WAAWC,iBAAiBF,EAAG,EAAEC;AACnCP,YAAAA,OAAAA,SAAAA,GAAAA,MAAMS,YAAY,oBAAoBF,QAAAA;IAAQ;EACpD,CACD;AAEK,QAAA;IAAEP,OAAOU,aAAa,CAAC;IAAG,GAAGC;EAAAA,IAAoBzD;AAIjD,QAAA;IACJ8C,OAAOY,mBAAmB,CAAC;IAC3BC,SAASC;IACT,GAAGC;EAAAA,IACD5D;AAEE,QAAA;IACJ0D,SAASG;IACTC,OAAOC;IACP,GAAGC;EAAAA,IACD/D;AAGA,MAAA,CAACqB,UAAU,EAAU,QAAA;AAEzB,QAAM2C,iBAAiBC,WAAW,MAAMlD,eAAAA,KAAoB,GAAG;AAEzDmD,QAAAA,iBAAiBD,WAAW,MAAM;AAC/BE,WAAAA,KACL5C,OAAO,EAAE6C,wBACT7C,OAAAA,EAAS8C,iCAAiC,CAAC,CAACzD,OAAAA,CAAQ,GACpDW,OAAS+C,EAAAA,+BAA+BnD,UAAU,GAClDI,OAAAA,EAASgD,gCACPtD,eAAAA,GACA+C,eAAAA,IAAmB,EACrB,CACF;EAAA,CACD;AAEKQ,QAAAA,iBAAiBP,WAAW,MAAM;AAC/B,WAAA;MACLjC,QAAQ,GAAGgC,eAAgB,CAAA;;MAE3B,GAAIV,cAAc,CAAA;IACpB;EAAA,CACD;AAEKmB,QAAAA,cAAcR,WAAW,MAAM;AACnC,WAAOE,KACL5C,OAAO,EAAEmD,cACTnD,OAAAA,EAASoD,qBAAqB1E,QAAQ,GACtCsB,OAAAA,EAASqD,sBAAsB,CAAC,CAAChE,OAAQ,CAAA,GACzCkD,qBACF;EAAA,CACD;AAED,SAAAe,gBACGC,SAAO;IACNC,WAAW5E;IAAS6E,KACfxE;IAAS,SAAA;IAAA,IAAAyE,WAAA;AAAA,aAAA,CAAAJ,gBAGbK,uBAAuBC,UAAQ;QAC9BC,OAAO;UACL1B,cAAcA,iBAAiB,MAAM;UAAC;QACxC;QAAC,IAAAuB,WAAA;AAAAJ,iBAAAA,gBAGAQ,iCAA+BC,WAAA;YAAAN,IAAAO,IAAA;AAAA,kBAAAC,QACzB9E;AAAQ,qBAAA8E,UAAAA,aAAAA,MAAAD,EAAA,IAAR7E,WAAQ6E;YAAAA;UAAAA,GACThC,iBAAe;YACnBnD;YACAC;YACAoF,WAAWvB;YACXtB,OAAO4B;YAAc,IACrB5D,SAAM;AAAA,qBAAEK,eAAe;YAAC;YACxBJ;YACAY,iBAAkBiE,CAAAA,MAAMjE,gBAAgBf,UAAUgF,CAAC;YACnDpF;UAAAA,CAAgC,CAAA;QAAA;MAAA,CAAA,IAAA,MAAA;AAAA,YAAAqF,OAAAC,QAAAA,GAAAC,QAAAF,KAAAG,YAAAC,QAAAF,MAAAC,YAAAE,QAAAD,MAAAE,aAAAC,QAAAL,MAAAI,aAAAE,QAAAD,MAAAD;AAAAN,eAAAA,MAAAL,WAS9BvB,wBAAsB;UAAA,cACf;UAA+B,WAChC2B,CAAM,MAAA;AACd7E,sBAAU,IAAI;AACd+C,6BAAiBA,cAAc8B,CAAC;UAClC;UAAC,KAAA,OAAA,IAAA;AAAA,mBACMjB,YAAY;UAAA;QAAC,CAAA,GAAA,OAAA,IAAA;AAAA2B,eAAAL,OAAAlB,gBAIfwB,cAAY,CAAA,CAAA,CAAA;AAAAD,eAAAJ,OAAAnB,gBAGZwB,cAAY,CAAA,CAAA,CAAA;AAAAC,2BAAAC,CAAA,QAAA;AAAA,cAAAC,MALLjF,OAAAA,EAASkF,2BAAyBC,OAChCnF,OAAAA,EAASoF,uBAAqBC,OAG9BrF,OAAO,EAAEsF,uBAAqBC,OAIhCvF,OAAAA,EAASwF,qBAAmBC,OAC5BzF,OAAAA,EAAS0F;AAAqBT,kBAAAD,IAAAb,KAAAwB,UAAArB,OAAAU,IAAAb,IAAAc,GAAA;AAAAE,mBAAAH,IAAAY,KAAAD,UAAAnB,OAAAQ,IAAAY,IAAAT,IAAA;AAAAE,mBAAAL,IAAAa,KAAAF,UAAAlB,OAAAO,IAAAa,IAAAR,IAAA;AAAAE,mBAAAP,IAAAc,KAAAH,UAAAhB,OAAAK,IAAAc,IAAAP,IAAA;AAAAE,mBAAAT,IAAAe,KAAAJ,UAAAf,OAAAI,IAAAe,IAAAN,IAAA;AAAAT,iBAAAA;QAAAA,GAAA;UAAAb,GAAA/E;UAAAwG,GAAAxG;UAAAyG,GAAAzG;UAAA0G,GAAA1G;UAAA2G,GAAA3G;QAAAA,CAAA;AAAAgF,eAAAA;MAAAA,GAAAA,CAAA;IAAA;EAAA,CAAA;AAIlD;", "names": ["TanStackLogo", "id", "createUniqueId", "_el$", "_tmpl$", "_el$2", "<PERSON><PERSON><PERSON><PERSON>", "_el$3", "_el$4", "nextS<PERSON>ling", "_el$5", "_el$6", "_el$7", "_el$8", "_el$9", "_el$0", "_el$1", "_el$10", "_el$11", "_el$12", "_el$13", "_el$14", "_el$15", "_el$16", "_el$17", "_el$18", "_el$19", "_el$20", "_el$21", "_el$22", "_el$23", "_el$24", "_el$25", "_el$26", "_el$27", "_el$28", "_el$29", "_el$30", "_el$31", "_el$32", "_el$33", "_el$34", "_el$35", "_el$36", "_el$37", "_el$38", "_el$39", "_el$40", "_el$41", "_el$42", "_el$43", "_el$44", "_el$45", "_el$46", "_el$47", "_el$48", "_el$49", "_el$50", "_el$51", "_el$52", "_el$53", "_el$54", "_el$55", "_el$56", "_el$57", "_el$58", "_el$59", "_el$60", "_el$61", "_el$62", "_el$63", "_el$64", "_el$65", "_el$66", "_el$67", "_el$68", "_el$69", "_el$70", "_el$71", "_el$72", "_el$73", "_el$74", "_el$75", "_el$76", "_el$77", "_el$78", "_el$79", "_el$80", "_el$81", "_el$82", "_el$83", "_el$84", "_el$85", "_el$86", "_el$87", "_el$88", "_el$89", "_$setAttribute", "FloatingTanStackRouterDevtools", "initialIsOpen", "panelProps", "closeButtonProps", "toggleButtonProps", "position", "containerElement", "Container", "router", "routerState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rootEl", "setRootEl", "createSignal", "panelRef", "undefined", "isOpen", "setIsOpen", "useLocalStorage", "devtoolsHeight", "setDevtoolsHeight", "isResolvedOpen", "setIsResolvedOpen", "isResizing", "setIsResizing", "isMounted", "useIsMounted", "styles", "useStyles", "handleDragStart", "panelElement", "startEvent", "button", "dragInfo", "originalHeight", "getBoundingClientRect", "height", "pageY", "run", "moveEvent", "delta", "newHeight", "unsub", "removeEventListener", "addEventListener", "createEffect", "previousValue", "parentElement", "style", "paddingBottom", "containerHeight", "prev", "window", "removeAttribute", "el", "fontSize", "getComputedStyle", "setProperty", "panelStyle", "otherPanelProps", "closeButtonStyle", "onClick", "onCloseClick", "otherCloseButtonProps", "onToggleClick", "class", "toggleButtonClassName", "otherToggleButtonProps", "resolvedHeight", "createMemo", "basePanelClass", "cx", "devtoolsPanelContainer", "devtoolsPanelContainerVisibility", "devtoolsPanelContainerResizing", "devtoolsPanelContainerAnimation", "basePanelStyle", "buttonStyle", "mainCloseBtn", "mainCloseBtnPosition", "mainCloseBtnAnimation", "_$createComponent", "Dynamic", "component", "ref", "children", "DevtoolsOnCloseContext", "Provider", "value", "BaseTanStackRouterDevtoolsPanel", "_$mergeProps", "r$", "_ref$", "className", "e", "_el$", "_tmpl$", "_el$2", "<PERSON><PERSON><PERSON><PERSON>", "_el$3", "_el$4", "nextS<PERSON>ling", "_el$5", "_el$6", "_$insert", "TanStackLogo", "_$effect", "_p$", "_v$", "mainCloseBtnIconContainer", "_v$2", "mainCloseBtnIconOuter", "_v$3", "mainCloseBtnIconInner", "_v$4", "mainCloseBtnDivider", "_v$5", "routerLogoCloseButton", "_$className", "t", "a", "o", "i"]}