import { useNavigate } from "@tanstack/react-router";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { blogService } from "@/services/blog-service";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Pencil, ArrowLeft } from "lucide-react";
import { Route } from "@/routes/_authenticated/blog/$blogId";
import { Loading } from "@/components/ui/loading";

export function BlogDetailPage() {
  const { blogId } = Route.useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data: blog, isLoading, error } = useQuery({
    queryKey: ["blog", blogId],
    queryFn: () => blogService.getBlogPost(blogId),
  });

  if (isLoading) {
    return (
      <Loading />
    );
  }

  if (error || !blog) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold text-red-600"><PERSON><PERSON> xảy ra lỗi</h2>
        <p className="text-gray-600">
          Không thể tải thông tin bài viết. Vui lòng thử lại sau.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => navigate({ to: "/blog" })}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-4 flex justify-between items-center">
        <Button
          variant="outline"
          onClick={() => navigate({ to: "/blog" })}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
        </Button>
        <div>
          <Button
            onClick={() => navigate({ to: `/blog/${blogId}/edit` })}
          >
            <Pencil className="mr-2 h-4 w-4" /> Chỉnh sửa
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl">{blog.title}</CardTitle>
          </div>
          <div className="text-sm text-muted-foreground">
            <span>Tác giả: {blog.authorName}</span>
            <span className="mx-2">•</span>
            <span>
              {blog.publishedAt
                ? `Xuất bản: ${new Date(blog.publishedAt).toLocaleDateString('vi-VN', { 
                    day: '2-digit', 
                    month: 'long', 
                    year: 'numeric' 
                  })}`
                : "Chưa xuất bản"}
            </span>
            <span className="mx-2">•</span>
            <span className="capitalize">Trạng thái: {blog.status}</span>
          </div>
        </CardHeader>
        <CardContent>
          {blog.featuredImage && (
            <div className="mb-6">
              <img
                src={blog.featuredImage}
                alt={blog.title}
                className="w-full max-h-80 object-cover rounded-md"
              />
            </div>
          )}
          <div className="prose max-w-none">
            {/* Hiển thị nội dung định dạng */}
            <div dangerouslySetInnerHTML={{ __html: blog.content }} />
          </div>
          {blog.tags && (
            <div className="mt-6 flex flex-wrap gap-2">
              {blog.tags.split(",").map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-muted rounded-md text-sm"
                >
                  {tag.trim()}
                </span>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 