{"version": 3, "sources": ["../../react-photo-album/dist/client/aggregate.js", "../../react-photo-album/dist/client/rows.js", "../../react-photo-album/dist/client/hooks.js", "../../react-photo-album/dist/static/index.js", "../../react-photo-album/dist/utils/index.js", "../../react-photo-album/dist/client/rowsProps.js", "../../react-photo-album/dist/layouts/rows.js", "../../react-photo-album/dist/client/columns.js", "../../react-photo-album/dist/client/columnsProps.js", "../../react-photo-album/dist/layouts/columns.js", "../../react-photo-album/dist/client/masonry.js", "../../react-photo-album/dist/client/masonryProps.js", "../../react-photo-album/dist/layouts/masonry.js"], "sourcesContent": ["\"use client\";\nimport { jsx } from \"react/jsx-runtime\";\nimport { forwardRef } from \"react\";\nimport RowsPhotoAlbum from \"./rows.js\";\nimport ColumnsPhotoAlbum from \"./columns.js\";\nimport MasonryPhotoAlbum from \"./masonry.js\";\nfunction PhotoAlbum({ layout, ...rest }, ref) {\n  if (layout === \"rows\") return jsx(RowsPhotoAlbum, { ref, ...rest });\n  if (layout === \"columns\") return jsx(ColumnsPhotoAlbum, { ref, ...rest });\n  if (layout === \"masonry\") return jsx(MasonryPhotoAlbum, { ref, ...rest });\n  return null;\n}\nconst PhotoAlbum$1 = forwardRef(PhotoAlbum);\nexport {\n  PhotoAlbum$1 as default\n};\n", "\"use client\";\nimport { jsx } from \"react/jsx-runtime\";\nimport { useMemo, forwardRef } from \"react\";\nimport { useContainerWidth } from \"./hooks.js\";\nimport StaticPhotoAlbum from \"../static/index.js\";\nimport resolveRowsProps from \"./rowsProps.js\";\nimport computeRowsLayout from \"../layouts/rows.js\";\nfunction RowsPhotoAlbum({ photos, breakpoints, defaultContainerWidth, ...rest }, ref) {\n  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints, defaultContainerWidth);\n  const { spacing, padding, targetRowHeight, minPhotos, maxPhotos, ...restProps } = resolveRowsProps(containerWidth, {\n    photos,\n    ...rest\n  });\n  const model = useMemo(\n    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && targetRowHeight !== void 0 ? computeRowsLayout(photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos) : void 0,\n    [photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos]\n  );\n  return jsx(StaticPhotoAlbum, { layout: \"rows\", ref: containerRef, model, ...restProps });\n}\nconst RowsPhotoAlbum$1 = forwardRef(RowsPhotoAlbum);\nexport {\n  RowsPhotoAlbum$1 as default\n};\n", "\"use client\";\nimport { useRef, useReducer, useCallback } from \"react\";\nfunction useArray(array) {\n  const ref = useRef(array);\n  if (!array || !ref.current || array.length !== ref.current.length || ref.current.some((el, i) => el !== array[i])) {\n    ref.current = array;\n  }\n  return ref.current;\n}\nfunction containerWidthReducer(state, [newContainerWidth, newScrollbarWidth]) {\n  const [containerWidth, scrollbarWidth] = state;\n  if (containerWidth !== void 0 && scrollbarWidth !== void 0 && newContainerWidth !== void 0 && newScrollbarWidth !== void 0 && newContainerWidth > containerWidth && newContainerWidth - containerWidth <= 20 && newScrollbarWidth < scrollbarWidth) {\n    return [containerWidth, newScrollbarWidth];\n  }\n  return containerWidth !== newContainerWidth || scrollbarWidth !== newScrollbarWidth ? [newContainerWidth, newScrollbarWidth] : state;\n}\nfunction resolveContainerWidth(el, breakpoints) {\n  let width = el?.clientWidth;\n  if (width !== void 0 && breakpoints && breakpoints.length > 0) {\n    const sorted = [...breakpoints.filter((x) => x > 0)].sort((a, b) => b - a);\n    sorted.push(Math.floor(sorted[sorted.length - 1] / 2));\n    width = sorted.find((breakpoint, index) => breakpoint <= width || index === sorted.length - 1);\n  }\n  return width;\n}\nfunction useContainerWidth(ref, breakpointsArray, defaultContainerWidth) {\n  const [[containerWidth], dispatch] = useReducer(containerWidthReducer, [defaultContainerWidth]);\n  const breakpoints = useArray(breakpointsArray);\n  const observerRef = useRef(void 0);\n  const containerRef = useCallback(\n    (node) => {\n      observerRef.current?.disconnect();\n      observerRef.current = void 0;\n      const updateWidth = () => dispatch([resolveContainerWidth(node, breakpoints), window.innerWidth - document.documentElement.clientWidth]);\n      updateWidth();\n      if (node && typeof ResizeObserver !== \"undefined\") {\n        observerRef.current = new ResizeObserver(updateWidth);\n        observerRef.current.observe(node);\n      }\n      if (typeof ref === \"function\") {\n        ref(node);\n      } else if (ref) {\n        ref.current = node;\n      }\n    },\n    [ref, breakpoints]\n  );\n  return { containerRef, containerWidth };\n}\nexport {\n  useArray,\n  useContainerWidth\n};\n", "import { jsx, jsxs } from \"react/jsx-runtime\";\nimport { forwardRef, createElement } from \"react\";\nimport { clsx, cssClass, cssVar, round, unwrap, srcSetAndSizes } from \"../utils/index.js\";\nfunction Component({\n  as,\n  render,\n  context,\n  classes = [],\n  variables = {},\n  style: styleProp,\n  className: classNameProp,\n  children,\n  ...rest\n}, ref) {\n  const className = clsx(\n    ...(Array.isArray(classes) ? classes : [classes]).filter((el) => typeof el === \"string\").map(cssClass),\n    classNameProp\n  );\n  const style = {\n    ...Object.fromEntries(\n      Object.entries(variables).map(([key, value]) => [\n        cssVar(key.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase()),\n        typeof value === \"number\" ? round(value, 5) : value\n      ])\n    ),\n    ...styleProp\n  };\n  const props = { style, className, children, ...rest };\n  if (render) {\n    const rendered = render({ ref, ...props }, context);\n    if (rendered) return rendered;\n  }\n  const Element = as || \"div\";\n  return jsx(Element, { ref, ...props });\n}\nconst Component$1 = forwardRef(Component);\nfunction PhotoComponent({\n  photo,\n  index,\n  width,\n  height,\n  onClick,\n  render: { wrapper, link, button, image, extras } = {},\n  componentsProps: { link: linkProps, button: buttonProps, wrapper: wrapperProps, image: imageProps } = {}\n}, ref) {\n  const { href } = photo;\n  const context = { photo, index, width: round(width, 3), height: round(height, 3) };\n  let props;\n  if (href) {\n    props = { ...linkProps, as: \"a\", render: link, classes: [\"photo\", \"link\"], href, onClick };\n  } else if (onClick) {\n    props = { ...buttonProps, as: \"button\", type: \"button\", render: button, classes: [\"photo\", \"button\"], onClick };\n  } else {\n    props = { ...wrapperProps, render: wrapper, classes: \"photo\" };\n  }\n  return jsxs(\n    Component$1,\n    {\n      ref,\n      variables: { photoWidth: context.width, photoHeight: context.height },\n      ...{ context, ...props },\n      children: [\n        jsx(Component$1, { as: \"img\", classes: \"image\", render: image, context, ...imageProps }),\n        extras?.({}, context)\n      ]\n    }\n  );\n}\nconst PhotoComponent$1 = forwardRef(PhotoComponent);\nfunction StaticPhotoAlbum({\n  layout,\n  sizes,\n  model,\n  skeleton,\n  onClick: onClickCallback,\n  render: { container, track, photo: renderPhoto, ...restRender } = {},\n  componentsProps: {\n    container: containerProps,\n    track: trackProps,\n    link: linkProps,\n    button: buttonProps,\n    wrapper: wrapperProps,\n    image: imageProps\n  } = {}\n}, ref) {\n  const { spacing, padding, containerWidth, tracks, variables, horizontal } = model || {};\n  return jsxs(\n    Component$1,\n    {\n      role: \"group\",\n      \"aria-label\": \"Photo album\",\n      ...containerProps,\n      variables: { spacing, padding, containerWidth, ...variables },\n      classes: [\"\", layout],\n      render: container,\n      ref,\n      children: [\n        spacing !== void 0 && padding !== void 0 && containerWidth !== void 0 && tracks?.map(({ photos, variables: trackVariables }, trackIndex) => {\n          const trackSize = photos.length;\n          const photosCount = horizontal ? trackSize : tracks.length;\n          return createElement(\n            Component$1,\n            {\n              ...trackProps,\n              key: trackIndex,\n              render: track,\n              classes: \"track\",\n              variables: { trackSize, ...trackVariables }\n            },\n            photos.map((context) => {\n              const { photo, index, width } = context;\n              const { key, src, alt, title, label } = photo;\n              const onClick = onClickCallback ? (event) => {\n                onClickCallback({ event, photo, index });\n              } : void 0;\n              if (renderPhoto) {\n                const rendered = renderPhoto({ onClick }, context);\n                if (rendered) return rendered;\n              }\n              const ariaLabel = (props) => {\n                return label ? { \"aria-label\": label, ...props } : props;\n              };\n              return jsx(\n                PhotoComponent$1,\n                {\n                  onClick,\n                  render: restRender,\n                  componentsProps: {\n                    image: {\n                      loading: \"lazy\",\n                      decoding: \"async\",\n                      src,\n                      alt,\n                      title,\n                      ...srcSetAndSizes(photo, sizes, width, containerWidth, photosCount, spacing, padding),\n                      ...unwrap(imageProps, context)\n                    },\n                    link: ariaLabel(unwrap(linkProps, context)),\n                    button: ariaLabel(unwrap(buttonProps, context)),\n                    wrapper: unwrap(wrapperProps, context)\n                  },\n                  ...context\n                },\n                key ?? src\n              );\n            })\n          );\n        }),\n        containerWidth === void 0 && skeleton\n      ]\n    }\n  );\n}\nconst StaticPhotoAlbum$1 = forwardRef(StaticPhotoAlbum);\nexport {\n  StaticPhotoAlbum$1 as default\n};\n", "function clsx(...classes) {\n  return [...classes].filter(Boolean).join(\" \");\n}\nfunction cssClass(suffix) {\n  return [\"react-photo-album\", suffix].filter(Boolean).join(\"--\");\n}\nfunction cssVar(suffix) {\n  return `--${cssClass(suffix)}`;\n}\nfunction ratio({ width, height }) {\n  return width / height;\n}\nconst breakpoints = Object.freeze([1200, 600, 300, 0]);\nfunction unwrap(value, arg) {\n  return typeof value === \"function\" ? value(arg) : value;\n}\nfunction unwrapParameter(value, containerWidth) {\n  return containerWidth !== void 0 ? unwrap(value, containerWidth) : void 0;\n}\nfunction selectResponsiveValue(values, containerWidth) {\n  const index = breakpoints.findIndex((breakpoint) => breakpoint <= containerWidth);\n  return unwrap(values[Math.max(index, 0)], containerWidth);\n}\nfunction resolveResponsiveParameter(parameter, containerWidth, values, minValue = 0) {\n  if (containerWidth === void 0) return void 0;\n  const value = unwrapParameter(parameter, containerWidth);\n  return Math.round(Math.max(value === void 0 ? selectResponsiveValue(values, containerWidth) : value, minValue));\n}\nfunction resolveCommonProps(containerWidth, {\n  spacing,\n  padding,\n  componentsProps,\n  render\n}) {\n  return {\n    spacing: resolveResponsiveParameter(spacing, containerWidth, [20, 15, 10, 5]),\n    padding: resolveResponsiveParameter(padding, containerWidth, [0, 0, 0, 0]),\n    componentsProps: unwrap(componentsProps, containerWidth) || {},\n    render: unwrap(render, containerWidth)\n  };\n}\nfunction round(value, decimals = 0) {\n  const factor = 10 ** decimals;\n  return Math.round((value + Number.EPSILON) * factor) / factor;\n}\nfunction srcSetAndSizes(photo, responsiveSizes, photoWidth, containerWidth, photosCount, spacing, padding) {\n  let srcSet;\n  let sizes;\n  const calcSizes = (base) => {\n    const gaps = spacing * (photosCount - 1) + 2 * padding * photosCount;\n    return `calc((${base.match(/^\\s*calc\\((.*)\\)\\s*$/)?.[1] ?? base} - ${gaps}px) / ${round((containerWidth - gaps) / photoWidth, 5)})`;\n  };\n  const images = photo.srcSet;\n  if (images && images.length > 0) {\n    srcSet = images.concat(\n      !images.some(({ width }) => width === photo.width) ? [{ src: photo.src, width: photo.width, height: photo.height }] : []\n    ).sort((first, second) => first.width - second.width).map((image) => `${image.src} ${image.width}w`).join(\", \");\n  }\n  if (responsiveSizes?.size) {\n    sizes = (responsiveSizes.sizes || []).map(({ viewport, size }) => `${viewport} ${calcSizes(size)}`).concat(calcSizes(responsiveSizes.size)).join(\", \");\n  } else {\n    sizes = `${Math.ceil(photoWidth / containerWidth * 100)}vw`;\n  }\n  return { srcSet, sizes };\n}\nexport {\n  clsx,\n  cssClass,\n  cssVar,\n  ratio,\n  resolveCommonProps,\n  resolveResponsiveParameter,\n  round,\n  srcSetAndSizes,\n  unwrap,\n  unwrapParameter\n};\n", "import { resolveCommonProps, unwrapParameter, resolveResponsiveParameter } from \"../utils/index.js\";\nfunction resolveRowsProps(containerWidth, { photos, targetRowHeight, rowConstraints, ...rest }) {\n  const { spacing, padding, componentsProps, render } = resolveCommonProps(containerWidth, rest);\n  const { singleRowMaxHeight, minPhotos, maxPhotos } = unwrapParameter(rowConstraints, containerWidth) || {};\n  if (singleRowMaxHeight !== void 0 && spacing !== void 0 && padding !== void 0) {\n    const maxWidth = Math.floor(\n      photos.reduce(\n        (acc, { width, height }) => acc + width / height * singleRowMaxHeight - 2 * padding,\n        padding * photos.length * 2 + spacing * (photos.length - 1)\n      )\n    );\n    if (maxWidth > 0) {\n      componentsProps.container = { ...componentsProps.container };\n      componentsProps.container.style = { maxWidth, ...componentsProps.container.style };\n    }\n  }\n  return {\n    ...rest,\n    targetRowHeight: resolveResponsiveParameter(targetRowHeight, containerWidth, [\n      (w) => w / 5,\n      (w) => w / 4,\n      (w) => w / 3,\n      (w) => w / 2\n    ]),\n    render,\n    spacing,\n    padding,\n    minPhotos,\n    maxPhotos,\n    componentsProps\n  };\n}\nexport {\n  resolveRowsProps as default\n};\n", "import { round, ratio } from \"../utils/index.js\";\nfunction rankingFunctionComparator(rank) {\n  return (a, b) => rank(b) - rank(a);\n}\nfunction MinHeap(comparator) {\n  let n = 0;\n  const heap = [];\n  const greater = (i, j) => comparator(heap[i], heap[j]) < 0;\n  const swap = (i, j) => {\n    const temp = heap[i];\n    heap[i] = heap[j];\n    heap[j] = temp;\n  };\n  const swim = (i) => {\n    let k = i;\n    let k2 = Math.floor(k / 2);\n    while (k > 1 && greater(k2, k)) {\n      swap(k2, k);\n      k = k2;\n      k2 = Math.floor(k / 2);\n    }\n  };\n  const sink = (i) => {\n    let k = i;\n    let k2 = k * 2;\n    while (k2 <= n) {\n      if (k2 < n && greater(k2, k2 + 1)) k2 += 1;\n      if (!greater(k, k2)) break;\n      swap(k, k2);\n      k = k2;\n      k2 = k * 2;\n    }\n  };\n  const push = (element) => {\n    n += 1;\n    heap[n] = element;\n    swim(n);\n  };\n  const pop = () => {\n    if (n === 0) return void 0;\n    swap(1, n);\n    n -= 1;\n    const max = heap.pop();\n    sink(1);\n    return max;\n  };\n  const size = () => n;\n  return { push, pop, size };\n}\nfunction buildPrecedentsMap(graph, startNode, endNode) {\n  const precedentsMap = /* @__PURE__ */ new Map();\n  const visited = /* @__PURE__ */ new Set();\n  const storedShortestPaths = /* @__PURE__ */ new Map();\n  storedShortestPaths.set(startNode, 0);\n  const queue = MinHeap(rankingFunctionComparator((el) => el[1]));\n  queue.push([startNode, 0]);\n  while (queue.size() > 0) {\n    const [id, weight] = queue.pop();\n    if (!visited.has(id)) {\n      const neighboringNodes = graph(id);\n      visited.add(id);\n      neighboringNodes.forEach((neighborWeight, neighbor) => {\n        const newWeight = weight + neighborWeight;\n        const currentId = precedentsMap.get(neighbor);\n        const currentWeight = storedShortestPaths.get(neighbor);\n        if (currentWeight === void 0 || currentWeight > newWeight && (currentWeight / newWeight > 1.005 || currentId !== void 0 && currentId < id)) {\n          storedShortestPaths.set(neighbor, newWeight);\n          queue.push([neighbor, newWeight]);\n          precedentsMap.set(neighbor, id);\n        }\n      });\n    }\n  }\n  return storedShortestPaths.has(endNode) ? precedentsMap : void 0;\n}\nfunction getPathFromPrecedentsMap(precedentsMap, endNode) {\n  if (!precedentsMap) return void 0;\n  const nodes = [];\n  for (let node = endNode; node !== void 0; node = precedentsMap.get(node)) {\n    nodes.push(node);\n  }\n  return nodes.reverse();\n}\nfunction findShortestPath(graph, startNode, endNode) {\n  return getPathFromPrecedentsMap(buildPrecedentsMap(graph, startNode, endNode), endNode);\n}\nfunction findIdealNodeSearch(photos, containerWidth, targetRowHeight, minPhotos) {\n  return round(containerWidth / targetRowHeight / Math.min(...photos.map((photo) => ratio(photo)))) + (minPhotos || 0) + 2;\n}\nfunction getCommonHeight(photos, containerWidth, spacing, padding) {\n  return (containerWidth - (photos.length - 1) * spacing - 2 * padding * photos.length) / photos.reduce((acc, photo) => acc + ratio(photo), 0);\n}\nfunction cost(photos, i, j, width, spacing, padding, targetRowHeight) {\n  const row = photos.slice(i, j);\n  const commonHeight = getCommonHeight(row, width, spacing, padding);\n  return commonHeight > 0 ? (commonHeight - targetRowHeight) ** 2 * row.length : void 0;\n}\nfunction makeGetRowNeighbors(photos, spacing, padding, containerWidth, targetRowHeight, limitNodeSearch, minPhotos, maxPhotos) {\n  return (node) => {\n    const results = /* @__PURE__ */ new Map();\n    results.set(node, 0);\n    const startOffset = minPhotos || 1;\n    const endOffset = Math.min(limitNodeSearch, maxPhotos || Infinity);\n    for (let i = node + startOffset; i < photos.length + 1; i += 1) {\n      if (i - node > endOffset) break;\n      const currentCost = cost(photos, node, i, containerWidth, spacing, padding, targetRowHeight);\n      if (currentCost === void 0) break;\n      results.set(i, currentCost);\n    }\n    return results;\n  };\n}\nfunction computeRowsLayout(photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos) {\n  const limitNodeSearch = findIdealNodeSearch(photos, containerWidth, targetRowHeight, minPhotos);\n  const getNeighbors = makeGetRowNeighbors(\n    photos,\n    spacing,\n    padding,\n    containerWidth,\n    targetRowHeight,\n    limitNodeSearch,\n    minPhotos,\n    maxPhotos\n  );\n  const path = findShortestPath(getNeighbors, 0, photos.length);\n  if (!path) return void 0;\n  const tracks = [];\n  for (let i = 1; i < path.length; i += 1) {\n    const row = photos.map((photo, index) => ({ photo, index })).slice(path[i - 1], path[i]);\n    const height = getCommonHeight(\n      row.map(({ photo }) => photo),\n      containerWidth,\n      spacing,\n      padding\n    );\n    tracks.push({\n      photos: row.map(({ photo, index }) => ({\n        photo,\n        index,\n        width: height * ratio(photo),\n        height\n      }))\n    });\n  }\n  return { spacing, padding, containerWidth, tracks, horizontal: true };\n}\nexport {\n  computeRowsLayout as default\n};\n", "\"use client\";\nimport { jsx } from \"react/jsx-runtime\";\nimport { useMemo, forwardRef } from \"react\";\nimport { useContainerWidth } from \"./hooks.js\";\nimport StaticPhotoAlbum from \"../static/index.js\";\nimport resolveColumnsProps from \"./columnsProps.js\";\nimport computeColumnsLayout from \"../layouts/columns.js\";\nfunction ColumnsPhotoAlbum({ photos, breakpoints, defaultContainerWidth, ...rest }, ref) {\n  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints, defaultContainerWidth);\n  const { spacing, padding, columns, ...restProps } = resolveColumnsProps(containerWidth, { photos, ...rest });\n  const model = useMemo(\n    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && columns !== void 0 ? computeColumnsLayout(photos, spacing, padding, containerWidth, columns) : void 0,\n    [photos, spacing, padding, containerWidth, columns]\n  );\n  return jsx(StaticPhotoAlbum, { layout: \"columns\", ref: containerRef, model, ...restProps });\n}\nconst ColumnsPhotoAlbum$1 = forwardRef(ColumnsPhotoAlbum);\nexport {\n  ColumnsPhotoAlbum$1 as default\n};\n", "import { resolveResponsiveParameter, resolveCommonProps } from \"../utils/index.js\";\nfunction resolveColumnsProps(containerWidth, { columns, ...rest }) {\n  return {\n    ...rest,\n    ...resolveCommonProps(containerWidth, rest),\n    columns: resolveResponsiveParameter(columns, containerWidth, [5, 4, 3, 2], 1)\n  };\n}\nexport {\n  resolveColumnsProps as default\n};\n", "import { ratio } from \"../utils/index.js\";\nfunction computeShortestPath(graph, pathLength, startNode, endNode) {\n  const matrix = /* @__PURE__ */ new Map();\n  const queue = /* @__PURE__ */ new Set();\n  queue.add(startNode);\n  for (let length = 0; length < pathLength; length += 1) {\n    const currentQueue = [...queue.keys()];\n    queue.clear();\n    currentQueue.forEach((node) => {\n      const accumulatedWeight = length > 0 ? matrix.get(node)[length][1] : 0;\n      graph(node).forEach(([neighbor, weight]) => {\n        let paths = matrix.get(neighbor);\n        if (!paths) {\n          paths = [];\n          matrix.set(neighbor, paths);\n        }\n        const newWeight = accumulatedWeight + weight;\n        const nextPath = paths[length + 1];\n        if (!nextPath || nextPath[1] > newWeight && (nextPath[1] / newWeight > 1.0001 || node < nextPath[0])) {\n          paths[length + 1] = [node, newWeight];\n        }\n        if (length < pathLength - 1 && neighbor !== endNode) {\n          queue.add(neighbor);\n        }\n      });\n    });\n  }\n  return matrix;\n}\nfunction reconstructShortestPath(matrix, pathLength, endNode) {\n  const path = [endNode];\n  for (let node = endNode, length = pathLength; length > 0; length -= 1) {\n    [node] = matrix.get(node)[length];\n    path.push(node);\n  }\n  return path.reverse();\n}\nfunction findShortestPathLengthN(graph, pathLength, startNode, endNode) {\n  return reconstructShortestPath(computeShortestPath(graph, pathLength, startNode, endNode), pathLength, endNode);\n}\nfunction makeGetColumnNeighbors(photos, spacing, padding, targetColumnWidth, targetColumnHeight) {\n  return (node) => {\n    const results = [];\n    const cutOffHeight = targetColumnHeight * 1.5;\n    let height = targetColumnWidth / ratio(photos[node]) + 2 * padding;\n    for (let i = node + 1; i < photos.length + 1; i += 1) {\n      results.push([i, (targetColumnHeight - height) ** 2]);\n      if (height > cutOffHeight || i === photos.length) {\n        break;\n      }\n      height += targetColumnWidth / ratio(photos[i]) + spacing + 2 * padding;\n    }\n    return results;\n  };\n}\nfunction buildColumnsModel(path, photos, spacing, padding, containerWidth, columnsGaps, columnsRatios) {\n  const tracks = [];\n  const totalRatio = columnsRatios.reduce((total, columnRatio) => total + columnRatio, 0);\n  for (let i = 0; i < path.length - 1; i += 1) {\n    const column = photos.map((photo, index) => ({ photo, index })).slice(path[i], path[i + 1]);\n    const adjustedGaps = columnsRatios.reduce(\n      (total, columnRatio, index) => total + (columnsGaps[i] - columnsGaps[index]) * columnRatio,\n      0\n    );\n    const columnWidth = (containerWidth - (path.length - 2) * spacing - 2 * (path.length - 1) * padding - adjustedGaps) * columnsRatios[i] / totalRatio;\n    tracks.push({\n      photos: column.map(({ photo, index }) => ({\n        photo,\n        index,\n        width: columnWidth,\n        height: columnWidth / ratio(photo)\n      })),\n      variables: { adjustedGaps, columnRatio: columnsRatios[i] }\n    });\n  }\n  return { tracks, variables: { totalRatio } };\n}\nfunction computeColumnsModel(photos, spacing, padding, containerWidth, targetColumnWidth, columns) {\n  const columnsGaps = [];\n  const columnsRatios = [];\n  if (photos.length <= columns) {\n    const averageRatio = photos.length > 0 ? photos.reduce((acc, photo) => acc + ratio(photo), 0) / photos.length : 1;\n    for (let i = 0; i < columns; i += 1) {\n      columnsGaps[i] = 2 * padding;\n      columnsRatios[i] = i < photos.length ? ratio(photos[i]) : averageRatio;\n    }\n    return buildColumnsModel(\n      Array.from({ length: columns + 1 }, (_, index) => Math.min(index, photos.length)),\n      photos,\n      spacing,\n      padding,\n      containerWidth,\n      columnsGaps,\n      columnsRatios\n    );\n  }\n  const targetColumnHeight = (photos.reduce((acc, photo) => acc + targetColumnWidth / ratio(photo), 0) + spacing * (photos.length - columns) + 2 * padding * photos.length) / columns;\n  const getNeighbors = makeGetColumnNeighbors(photos, spacing, padding, targetColumnWidth, targetColumnHeight);\n  const path = findShortestPathLengthN(getNeighbors, columns, 0, photos.length);\n  for (let i = 0; i < path.length - 1; i += 1) {\n    const column = photos.slice(path[i], path[i + 1]);\n    columnsGaps[i] = spacing * (column.length - 1) + 2 * padding * column.length;\n    columnsRatios[i] = 1 / column.reduce((acc, photo) => acc + 1 / ratio(photo), 0);\n  }\n  return buildColumnsModel(path, photos, spacing, padding, containerWidth, columnsGaps, columnsRatios);\n}\nfunction computeColumnsLayout(photos, spacing, padding, containerWidth, columns) {\n  const targetColumnWidth = (containerWidth - spacing * (columns - 1) - 2 * padding * columns) / columns;\n  const { tracks, variables } = computeColumnsModel(\n    photos,\n    spacing,\n    padding,\n    containerWidth,\n    targetColumnWidth,\n    columns\n  );\n  if (tracks.some((track) => track.photos.some(({ width, height }) => width < 0 || height < 0))) {\n    return columns > 1 ? computeColumnsLayout(photos, spacing, padding, containerWidth, columns - 1) : void 0;\n  }\n  return { tracks, spacing, padding, containerWidth, variables: { columns, ...variables } };\n}\nexport {\n  computeColumnsLayout as default\n};\n", "\"use client\";\nimport { jsx } from \"react/jsx-runtime\";\nimport { useMemo, forwardRef } from \"react\";\nimport { useContainerWidth } from \"./hooks.js\";\nimport StaticPhotoAlbum from \"../static/index.js\";\nimport resolveMasonryProps from \"./masonryProps.js\";\nimport computeMasonryLayout from \"../layouts/masonry.js\";\nfunction MasonryPhotoAlbum({ photos, breakpoints, defaultContainerWidth, ...rest }, ref) {\n  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints, defaultContainerWidth);\n  const { spacing, padding, columns, ...restProps } = resolveMasonryProps(containerWidth, { photos, ...rest });\n  const model = useMemo(\n    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && columns !== void 0 ? computeMasonryLayout(photos, spacing, padding, containerWidth, columns) : void 0,\n    [photos, spacing, padding, containerWidth, columns]\n  );\n  return jsx(StaticPhotoAlbum, { layout: \"masonry\", ref: containerRef, model, ...restProps });\n}\nconst MasonryPhotoAlbum$1 = forwardRef(MasonryPhotoAlbum);\nexport {\n  MasonryPhotoAlbum$1 as default\n};\n", "import { resolveResponsiveParameter, resolveCommonProps } from \"../utils/index.js\";\nfunction resolveMasonryProps(containerWidth, { columns, ...rest }) {\n  return {\n    ...rest,\n    ...resolveCommonProps(containerWidth, rest),\n    columns: resolveResponsiveParameter(columns, containerWidth, [5, 4, 3, 2], 1)\n  };\n}\nexport {\n  resolveMasonryProps as default\n};\n", "import { ratio } from \"../utils/index.js\";\nfunction computeMasonryLayout(photos, spacing, padding, containerWidth, columns) {\n  const columnWidth = (containerWidth - spacing * (columns - 1) - 2 * padding * columns) / columns;\n  if (columnWidth <= 0) {\n    return columns > 1 ? computeMasonryLayout(photos, spacing, padding, containerWidth, columns - 1) : void 0;\n  }\n  const columnsCurrentTopPositions = [];\n  for (let i = 0; i < columns; i += 1) {\n    columnsCurrentTopPositions[i] = 0;\n  }\n  const columnsModel = photos.reduce(\n    (model, photo, index) => {\n      const shortestColumn = columnsCurrentTopPositions.reduce(\n        (currentShortestColumn, item, i) => item < columnsCurrentTopPositions[currentShortestColumn] - 1 ? i : currentShortestColumn,\n        0\n      );\n      columnsCurrentTopPositions[shortestColumn] = columnsCurrentTopPositions[shortestColumn] + columnWidth / ratio(photo) + spacing + 2 * padding;\n      model[shortestColumn].push({ photo, index });\n      return model;\n    },\n    Array.from({ length: columns }, () => [])\n  );\n  return {\n    spacing,\n    padding,\n    containerWidth,\n    variables: { columns },\n    tracks: columnsModel.map((column) => ({\n      photos: column.map(({ photo, index }) => ({\n        photo,\n        index,\n        width: columnWidth,\n        height: columnWidth / ratio(photo)\n      }))\n    }))\n  };\n}\nexport {\n  computeMasonryLayout as default\n};\n"], "mappings": ";;;;;;;;;;;AACA,IAAAA,sBAAoB;AACpB,IAAAC,gBAA2B;;;ACD3B,IAAAC,sBAAoB;AACpB,IAAAC,gBAAoC;;;ACDpC,mBAAgD;AAChD,SAAS,SAAS,OAAO;AACvB,QAAM,UAAM,qBAAO,KAAK;AACxB,MAAI,CAAC,SAAS,CAAC,IAAI,WAAW,MAAM,WAAW,IAAI,QAAQ,UAAU,IAAI,QAAQ,KAAK,CAAC,IAAI,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG;AACjH,QAAI,UAAU;AAAA,EAChB;AACA,SAAO,IAAI;AACb;AACA,SAAS,sBAAsB,OAAO,CAAC,mBAAmB,iBAAiB,GAAG;AAC5E,QAAM,CAAC,gBAAgB,cAAc,IAAI;AACzC,MAAI,mBAAmB,UAAU,mBAAmB,UAAU,sBAAsB,UAAU,sBAAsB,UAAU,oBAAoB,kBAAkB,oBAAoB,kBAAkB,MAAM,oBAAoB,gBAAgB;AAClP,WAAO,CAAC,gBAAgB,iBAAiB;AAAA,EAC3C;AACA,SAAO,mBAAmB,qBAAqB,mBAAmB,oBAAoB,CAAC,mBAAmB,iBAAiB,IAAI;AACjI;AACA,SAAS,sBAAsB,IAAIC,cAAa;AAC9C,MAAI,QAAQ,yBAAI;AAChB,MAAI,UAAU,UAAUA,gBAAeA,aAAY,SAAS,GAAG;AAC7D,UAAM,SAAS,CAAC,GAAGA,aAAY,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACzE,WAAO,KAAK,KAAK,MAAM,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;AACrD,YAAQ,OAAO,KAAK,CAAC,YAAY,UAAU,cAAc,SAAS,UAAU,OAAO,SAAS,CAAC;AAAA,EAC/F;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,KAAK,kBAAkB,uBAAuB;AACvE,QAAM,CAAC,CAAC,cAAc,GAAG,QAAQ,QAAI,yBAAW,uBAAuB,CAAC,qBAAqB,CAAC;AAC9F,QAAMA,eAAc,SAAS,gBAAgB;AAC7C,QAAM,kBAAc,qBAAO,MAAM;AACjC,QAAM,mBAAe;AAAA,IACnB,CAAC,SAAS;AA9Bd;AA+BM,wBAAY,YAAZ,mBAAqB;AACrB,kBAAY,UAAU;AACtB,YAAM,cAAc,MAAM,SAAS,CAAC,sBAAsB,MAAMA,YAAW,GAAG,OAAO,aAAa,SAAS,gBAAgB,WAAW,CAAC;AACvI,kBAAY;AACZ,UAAI,QAAQ,OAAO,mBAAmB,aAAa;AACjD,oBAAY,UAAU,IAAI,eAAe,WAAW;AACpD,oBAAY,QAAQ,QAAQ,IAAI;AAAA,MAClC;AACA,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,IAAI;AAAA,MACV,WAAW,KAAK;AACd,YAAI,UAAU;AAAA,MAChB;AAAA,IACF;AAAA,IACA,CAAC,KAAKA,YAAW;AAAA,EACnB;AACA,SAAO,EAAE,cAAc,eAAe;AACxC;;;AChDA,yBAA0B;AAC1B,IAAAC,gBAA0C;;;ACD1C,SAAS,QAAQ,SAAS;AACxB,SAAO,CAAC,GAAG,OAAO,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC9C;AACA,SAAS,SAAS,QAAQ;AACxB,SAAO,CAAC,qBAAqB,MAAM,EAAE,OAAO,OAAO,EAAE,KAAK,IAAI;AAChE;AACA,SAAS,OAAO,QAAQ;AACtB,SAAO,KAAK,SAAS,MAAM,CAAC;AAC9B;AACA,SAAS,MAAM,EAAE,OAAO,OAAO,GAAG;AAChC,SAAO,QAAQ;AACjB;AACA,IAAM,cAAc,OAAO,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;AACrD,SAAS,OAAO,OAAO,KAAK;AAC1B,SAAO,OAAO,UAAU,aAAa,MAAM,GAAG,IAAI;AACpD;AACA,SAAS,gBAAgB,OAAO,gBAAgB;AAC9C,SAAO,mBAAmB,SAAS,OAAO,OAAO,cAAc,IAAI;AACrE;AACA,SAAS,sBAAsB,QAAQ,gBAAgB;AACrD,QAAM,QAAQ,YAAY,UAAU,CAAC,eAAe,cAAc,cAAc;AAChF,SAAO,OAAO,OAAO,KAAK,IAAI,OAAO,CAAC,CAAC,GAAG,cAAc;AAC1D;AACA,SAAS,2BAA2B,WAAW,gBAAgB,QAAQ,WAAW,GAAG;AACnF,MAAI,mBAAmB,OAAQ,QAAO;AACtC,QAAM,QAAQ,gBAAgB,WAAW,cAAc;AACvD,SAAO,KAAK,MAAM,KAAK,IAAI,UAAU,SAAS,sBAAsB,QAAQ,cAAc,IAAI,OAAO,QAAQ,CAAC;AAChH;AACA,SAAS,mBAAmB,gBAAgB;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL,SAAS,2BAA2B,SAAS,gBAAgB,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,IAC5E,SAAS,2BAA2B,SAAS,gBAAgB,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IACzE,iBAAiB,OAAO,iBAAiB,cAAc,KAAK,CAAC;AAAA,IAC7D,QAAQ,OAAO,QAAQ,cAAc;AAAA,EACvC;AACF;AACA,SAAS,MAAM,OAAO,WAAW,GAAG;AAClC,QAAM,SAAS,MAAM;AACrB,SAAO,KAAK,OAAO,QAAQ,OAAO,WAAW,MAAM,IAAI;AACzD;AACA,SAAS,eAAe,OAAO,iBAAiB,YAAY,gBAAgB,aAAa,SAAS,SAAS;AACzG,MAAI;AACJ,MAAI;AACJ,QAAM,YAAY,CAAC,SAAS;AAhD9B;AAiDI,UAAM,OAAO,WAAW,cAAc,KAAK,IAAI,UAAU;AACzD,WAAO,WAAS,UAAK,MAAM,sBAAsB,MAAjC,mBAAqC,OAAM,IAAI,MAAM,IAAI,SAAS,OAAO,iBAAiB,QAAQ,YAAY,CAAC,CAAC;AAAA,EAClI;AACA,QAAM,SAAS,MAAM;AACrB,MAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,aAAS,OAAO;AAAA,MACd,CAAC,OAAO,KAAK,CAAC,EAAE,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,OAAO,MAAM,OAAO,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC;AAAA,IACzH,EAAE,KAAK,CAAC,OAAO,WAAW,MAAM,QAAQ,OAAO,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,MAAM,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE,KAAK,IAAI;AAAA,EAChH;AACA,MAAI,mDAAiB,MAAM;AACzB,aAAS,gBAAgB,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,UAAU,KAAK,MAAM,GAAG,QAAQ,IAAI,UAAU,IAAI,CAAC,EAAE,EAAE,OAAO,UAAU,gBAAgB,IAAI,CAAC,EAAE,KAAK,IAAI;AAAA,EACvJ,OAAO;AACL,YAAQ,GAAG,KAAK,KAAK,aAAa,iBAAiB,GAAG,CAAC;AAAA,EACzD;AACA,SAAO,EAAE,QAAQ,MAAM;AACzB;;;AD7DA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,CAAC;AAAA,EACX,YAAY,CAAC;AAAA,EACb,OAAO;AAAA,EACP,WAAW;AAAA,EACX;AAAA,EACA,GAAG;AACL,GAAG,KAAK;AACN,QAAM,YAAY;AAAA,IAChB,IAAI,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,OAAO,OAAO,QAAQ,EAAE,IAAI,QAAQ;AAAA,IACrG;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ,GAAG,OAAO;AAAA,MACR,OAAO,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAAA,QAC9C,OAAO,IAAI,QAAQ,mBAAmB,OAAO,EAAE,YAAY,CAAC;AAAA,QAC5D,OAAO,UAAU,WAAW,MAAM,OAAO,CAAC,IAAI;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,QAAQ,EAAE,OAAO,WAAW,UAAU,GAAG,KAAK;AACpD,MAAI,QAAQ;AACV,UAAM,WAAW,OAAO,EAAE,KAAK,GAAG,MAAM,GAAG,OAAO;AAClD,QAAI,SAAU,QAAO;AAAA,EACvB;AACA,QAAM,UAAU,MAAM;AACtB,aAAO,wBAAI,SAAS,EAAE,KAAK,GAAG,MAAM,CAAC;AACvC;AACA,IAAM,kBAAc,0BAAW,SAAS;AACxC,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,EAAE,SAAS,MAAM,QAAQ,OAAO,OAAO,IAAI,CAAC;AAAA,EACpD,iBAAiB,EAAE,MAAM,WAAW,QAAQ,aAAa,SAAS,cAAc,OAAO,WAAW,IAAI,CAAC;AACzG,GAAG,KAAK;AACN,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,UAAU,EAAE,OAAO,OAAO,OAAO,MAAM,OAAO,CAAC,GAAG,QAAQ,MAAM,QAAQ,CAAC,EAAE;AACjF,MAAI;AACJ,MAAI,MAAM;AACR,YAAQ,EAAE,GAAG,WAAW,IAAI,KAAK,QAAQ,MAAM,SAAS,CAAC,SAAS,MAAM,GAAG,MAAM,QAAQ;AAAA,EAC3F,WAAW,SAAS;AAClB,YAAQ,EAAE,GAAG,aAAa,IAAI,UAAU,MAAM,UAAU,QAAQ,QAAQ,SAAS,CAAC,SAAS,QAAQ,GAAG,QAAQ;AAAA,EAChH,OAAO;AACL,YAAQ,EAAE,GAAG,cAAc,QAAQ,SAAS,SAAS,QAAQ;AAAA,EAC/D;AACA,aAAO;AAAA,IACL;AAAA,IACA;AAAA,MACE;AAAA,MACA,WAAW,EAAE,YAAY,QAAQ,OAAO,aAAa,QAAQ,OAAO;AAAA,MACpE,GAAG,EAAE,SAAS,GAAG,MAAM;AAAA,MACvB,UAAU;AAAA,YACR,wBAAI,aAAa,EAAE,IAAI,OAAO,SAAS,SAAS,QAAQ,OAAO,SAAS,GAAG,WAAW,CAAC;AAAA,QACvF,iCAAS,CAAC,GAAG;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,uBAAmB,0BAAW,cAAc;AAClD,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,QAAQ,EAAE,WAAW,OAAO,OAAO,aAAa,GAAG,WAAW,IAAI,CAAC;AAAA,EACnE,iBAAiB;AAAA,IACf,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,CAAC;AACP,GAAG,KAAK;AACN,QAAM,EAAE,SAAS,SAAS,gBAAgB,QAAQ,WAAW,WAAW,IAAI,SAAS,CAAC;AACtF,aAAO;AAAA,IACL;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,cAAc;AAAA,MACd,GAAG;AAAA,MACH,WAAW,EAAE,SAAS,SAAS,gBAAgB,GAAG,UAAU;AAAA,MAC5D,SAAS,CAAC,IAAI,MAAM;AAAA,MACpB,QAAQ;AAAA,MACR;AAAA,MACA,UAAU;AAAA,QACR,YAAY,UAAU,YAAY,UAAU,mBAAmB,WAAU,iCAAQ,IAAI,CAAC,EAAE,QAAQ,WAAW,eAAe,GAAG,eAAe;AAC1I,gBAAM,YAAY,OAAO;AACzB,gBAAM,cAAc,aAAa,YAAY,OAAO;AACpD,qBAAO;AAAA,YACL;AAAA,YACA;AAAA,cACE,GAAG;AAAA,cACH,KAAK;AAAA,cACL,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,WAAW,EAAE,WAAW,GAAG,eAAe;AAAA,YAC5C;AAAA,YACA,OAAO,IAAI,CAAC,YAAY;AACtB,oBAAM,EAAE,OAAO,OAAO,MAAM,IAAI;AAChC,oBAAM,EAAE,KAAK,KAAK,KAAK,OAAO,MAAM,IAAI;AACxC,oBAAM,UAAU,kBAAkB,CAAC,UAAU;AAC3C,gCAAgB,EAAE,OAAO,OAAO,MAAM,CAAC;AAAA,cACzC,IAAI;AACJ,kBAAI,aAAa;AACf,sBAAM,WAAW,YAAY,EAAE,QAAQ,GAAG,OAAO;AACjD,oBAAI,SAAU,QAAO;AAAA,cACvB;AACA,oBAAM,YAAY,CAAC,UAAU;AAC3B,uBAAO,QAAQ,EAAE,cAAc,OAAO,GAAG,MAAM,IAAI;AAAA,cACrD;AACA,yBAAO;AAAA,gBACL;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA,QAAQ;AAAA,kBACR,iBAAiB;AAAA,oBACf,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,UAAU;AAAA,sBACV;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA,GAAG,eAAe,OAAO,OAAO,OAAO,gBAAgB,aAAa,SAAS,OAAO;AAAA,sBACpF,GAAG,OAAO,YAAY,OAAO;AAAA,oBAC/B;AAAA,oBACA,MAAM,UAAU,OAAO,WAAW,OAAO,CAAC;AAAA,oBAC1C,QAAQ,UAAU,OAAO,aAAa,OAAO,CAAC;AAAA,oBAC9C,SAAS,OAAO,cAAc,OAAO;AAAA,kBACvC;AAAA,kBACA,GAAG;AAAA,gBACL;AAAA,gBACA,OAAO;AAAA,cACT;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,mBAAmB,UAAU;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,yBAAqB,0BAAW,gBAAgB;;;AExJtD,SAAS,iBAAiB,gBAAgB,EAAE,QAAQ,iBAAiB,gBAAgB,GAAG,KAAK,GAAG;AAC9F,QAAM,EAAE,SAAS,SAAS,iBAAiB,OAAO,IAAI,mBAAmB,gBAAgB,IAAI;AAC7F,QAAM,EAAE,oBAAoB,WAAW,UAAU,IAAI,gBAAgB,gBAAgB,cAAc,KAAK,CAAC;AACzG,MAAI,uBAAuB,UAAU,YAAY,UAAU,YAAY,QAAQ;AAC7E,UAAM,WAAW,KAAK;AAAA,MACpB,OAAO;AAAA,QACL,CAAC,KAAK,EAAE,OAAO,OAAO,MAAM,MAAM,QAAQ,SAAS,qBAAqB,IAAI;AAAA,QAC5E,UAAU,OAAO,SAAS,IAAI,WAAW,OAAO,SAAS;AAAA,MAC3D;AAAA,IACF;AACA,QAAI,WAAW,GAAG;AAChB,sBAAgB,YAAY,EAAE,GAAG,gBAAgB,UAAU;AAC3D,sBAAgB,UAAU,QAAQ,EAAE,UAAU,GAAG,gBAAgB,UAAU,MAAM;AAAA,IACnF;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,iBAAiB,2BAA2B,iBAAiB,gBAAgB;AAAA,MAC3E,CAAC,MAAM,IAAI;AAAA,MACX,CAAC,MAAM,IAAI;AAAA,MACX,CAAC,MAAM,IAAI;AAAA,MACX,CAAC,MAAM,IAAI;AAAA,IACb,CAAC;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC9BA,SAAS,0BAA0B,MAAM;AACvC,SAAO,CAAC,GAAG,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC;AACnC;AACA,SAAS,QAAQ,YAAY;AAC3B,MAAI,IAAI;AACR,QAAM,OAAO,CAAC;AACd,QAAM,UAAU,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI;AACzD,QAAM,OAAO,CAAC,GAAG,MAAM;AACrB,UAAM,OAAO,KAAK,CAAC;AACnB,SAAK,CAAC,IAAI,KAAK,CAAC;AAChB,SAAK,CAAC,IAAI;AAAA,EACZ;AACA,QAAM,OAAO,CAAC,MAAM;AAClB,QAAI,IAAI;AACR,QAAI,KAAK,KAAK,MAAM,IAAI,CAAC;AACzB,WAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,GAAG;AAC9B,WAAK,IAAI,CAAC;AACV,UAAI;AACJ,WAAK,KAAK,MAAM,IAAI,CAAC;AAAA,IACvB;AAAA,EACF;AACA,QAAM,OAAO,CAAC,MAAM;AAClB,QAAI,IAAI;AACR,QAAI,KAAK,IAAI;AACb,WAAO,MAAM,GAAG;AACd,UAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,EAAG,OAAM;AACzC,UAAI,CAAC,QAAQ,GAAG,EAAE,EAAG;AACrB,WAAK,GAAG,EAAE;AACV,UAAI;AACJ,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AACA,QAAM,OAAO,CAAC,YAAY;AACxB,SAAK;AACL,SAAK,CAAC,IAAI;AACV,SAAK,CAAC;AAAA,EACR;AACA,QAAM,MAAM,MAAM;AAChB,QAAI,MAAM,EAAG,QAAO;AACpB,SAAK,GAAG,CAAC;AACT,SAAK;AACL,UAAM,MAAM,KAAK,IAAI;AACrB,SAAK,CAAC;AACN,WAAO;AAAA,EACT;AACA,QAAM,OAAO,MAAM;AACnB,SAAO,EAAE,MAAM,KAAK,KAAK;AAC3B;AACA,SAAS,mBAAmB,OAAO,WAAW,SAAS;AACrD,QAAM,gBAAgC,oBAAI,IAAI;AAC9C,QAAM,UAA0B,oBAAI,IAAI;AACxC,QAAM,sBAAsC,oBAAI,IAAI;AACpD,sBAAoB,IAAI,WAAW,CAAC;AACpC,QAAM,QAAQ,QAAQ,0BAA0B,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;AAC9D,QAAM,KAAK,CAAC,WAAW,CAAC,CAAC;AACzB,SAAO,MAAM,KAAK,IAAI,GAAG;AACvB,UAAM,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI;AAC/B,QAAI,CAAC,QAAQ,IAAI,EAAE,GAAG;AACpB,YAAM,mBAAmB,MAAM,EAAE;AACjC,cAAQ,IAAI,EAAE;AACd,uBAAiB,QAAQ,CAAC,gBAAgB,aAAa;AACrD,cAAM,YAAY,SAAS;AAC3B,cAAM,YAAY,cAAc,IAAI,QAAQ;AAC5C,cAAM,gBAAgB,oBAAoB,IAAI,QAAQ;AACtD,YAAI,kBAAkB,UAAU,gBAAgB,cAAc,gBAAgB,YAAY,SAAS,cAAc,UAAU,YAAY,KAAK;AAC1I,8BAAoB,IAAI,UAAU,SAAS;AAC3C,gBAAM,KAAK,CAAC,UAAU,SAAS,CAAC;AAChC,wBAAc,IAAI,UAAU,EAAE;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,oBAAoB,IAAI,OAAO,IAAI,gBAAgB;AAC5D;AACA,SAAS,yBAAyB,eAAe,SAAS;AACxD,MAAI,CAAC,cAAe,QAAO;AAC3B,QAAM,QAAQ,CAAC;AACf,WAAS,OAAO,SAAS,SAAS,QAAQ,OAAO,cAAc,IAAI,IAAI,GAAG;AACxE,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO,MAAM,QAAQ;AACvB;AACA,SAAS,iBAAiB,OAAO,WAAW,SAAS;AACnD,SAAO,yBAAyB,mBAAmB,OAAO,WAAW,OAAO,GAAG,OAAO;AACxF;AACA,SAAS,oBAAoB,QAAQ,gBAAgB,iBAAiB,WAAW;AAC/E,SAAO,MAAM,iBAAiB,kBAAkB,KAAK,IAAI,GAAG,OAAO,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC,KAAK,aAAa,KAAK;AACzH;AACA,SAAS,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS;AACjE,UAAQ,kBAAkB,OAAO,SAAS,KAAK,UAAU,IAAI,UAAU,OAAO,UAAU,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,MAAM,KAAK,GAAG,CAAC;AAC7I;AACA,SAAS,KAAK,QAAQ,GAAG,GAAG,OAAO,SAAS,SAAS,iBAAiB;AACpE,QAAM,MAAM,OAAO,MAAM,GAAG,CAAC;AAC7B,QAAM,eAAe,gBAAgB,KAAK,OAAO,SAAS,OAAO;AACjE,SAAO,eAAe,KAAK,eAAe,oBAAoB,IAAI,IAAI,SAAS;AACjF;AACA,SAAS,oBAAoB,QAAQ,SAAS,SAAS,gBAAgB,iBAAiB,iBAAiB,WAAW,WAAW;AAC7H,SAAO,CAAC,SAAS;AACf,UAAM,UAA0B,oBAAI,IAAI;AACxC,YAAQ,IAAI,MAAM,CAAC;AACnB,UAAM,cAAc,aAAa;AACjC,UAAM,YAAY,KAAK,IAAI,iBAAiB,aAAa,QAAQ;AACjE,aAAS,IAAI,OAAO,aAAa,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG;AAC9D,UAAI,IAAI,OAAO,UAAW;AAC1B,YAAM,cAAc,KAAK,QAAQ,MAAM,GAAG,gBAAgB,SAAS,SAAS,eAAe;AAC3F,UAAI,gBAAgB,OAAQ;AAC5B,cAAQ,IAAI,GAAG,WAAW;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,kBAAkB,QAAQ,SAAS,SAAS,gBAAgB,iBAAiB,WAAW,WAAW;AAC1G,QAAM,kBAAkB,oBAAoB,QAAQ,gBAAgB,iBAAiB,SAAS;AAC9F,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,OAAO,iBAAiB,cAAc,GAAG,OAAO,MAAM;AAC5D,MAAI,CAAC,KAAM,QAAO;AAClB,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,UAAM,MAAM,OAAO,IAAI,CAAC,OAAO,WAAW,EAAE,OAAO,MAAM,EAAE,EAAE,MAAM,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AACvF,UAAM,SAAS;AAAA,MACb,IAAI,IAAI,CAAC,EAAE,MAAM,MAAM,KAAK;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,KAAK;AAAA,MACV,QAAQ,IAAI,IAAI,CAAC,EAAE,OAAO,MAAM,OAAO;AAAA,QACrC;AAAA,QACA;AAAA,QACA,OAAO,SAAS,MAAM,KAAK;AAAA,QAC3B;AAAA,MACF,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AACA,SAAO,EAAE,SAAS,SAAS,gBAAgB,QAAQ,YAAY,KAAK;AACtE;;;AL1IA,SAAS,eAAe,EAAE,QAAQ,aAAAC,cAAa,uBAAuB,GAAG,KAAK,GAAG,KAAK;AACpF,QAAM,EAAE,cAAc,eAAe,IAAI,kBAAkB,KAAKA,cAAa,qBAAqB;AAClG,QAAM,EAAE,SAAS,SAAS,iBAAiB,WAAW,WAAW,GAAG,UAAU,IAAI,iBAAiB,gBAAgB;AAAA,IACjH;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACD,QAAM,YAAQ;AAAA,IACZ,MAAM,mBAAmB,UAAU,YAAY,UAAU,YAAY,UAAU,oBAAoB,SAAS,kBAAkB,QAAQ,SAAS,SAAS,gBAAgB,iBAAiB,WAAW,SAAS,IAAI;AAAA,IACjN,CAAC,QAAQ,SAAS,SAAS,gBAAgB,iBAAiB,WAAW,SAAS;AAAA,EAClF;AACA,aAAO,yBAAI,oBAAkB,EAAE,QAAQ,QAAQ,KAAK,cAAc,OAAO,GAAG,UAAU,CAAC;AACzF;AACA,IAAM,uBAAmB,0BAAW,cAAc;;;AMlBlD,IAAAC,sBAAoB;AACpB,IAAAC,gBAAoC;;;ACDpC,SAAS,oBAAoB,gBAAgB,EAAE,SAAS,GAAG,KAAK,GAAG;AACjE,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG,mBAAmB,gBAAgB,IAAI;AAAA,IAC1C,SAAS,2BAA2B,SAAS,gBAAgB,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AAAA,EAC9E;AACF;;;ACNA,SAAS,oBAAoB,OAAO,YAAY,WAAW,SAAS;AAClE,QAAM,SAAyB,oBAAI,IAAI;AACvC,QAAM,QAAwB,oBAAI,IAAI;AACtC,QAAM,IAAI,SAAS;AACnB,WAAS,SAAS,GAAG,SAAS,YAAY,UAAU,GAAG;AACrD,UAAM,eAAe,CAAC,GAAG,MAAM,KAAK,CAAC;AACrC,UAAM,MAAM;AACZ,iBAAa,QAAQ,CAAC,SAAS;AAC7B,YAAM,oBAAoB,SAAS,IAAI,OAAO,IAAI,IAAI,EAAE,MAAM,EAAE,CAAC,IAAI;AACrE,YAAM,IAAI,EAAE,QAAQ,CAAC,CAAC,UAAU,MAAM,MAAM;AAC1C,YAAI,QAAQ,OAAO,IAAI,QAAQ;AAC/B,YAAI,CAAC,OAAO;AACV,kBAAQ,CAAC;AACT,iBAAO,IAAI,UAAU,KAAK;AAAA,QAC5B;AACA,cAAM,YAAY,oBAAoB;AACtC,cAAM,WAAW,MAAM,SAAS,CAAC;AACjC,YAAI,CAAC,YAAY,SAAS,CAAC,IAAI,cAAc,SAAS,CAAC,IAAI,YAAY,UAAU,OAAO,SAAS,CAAC,IAAI;AACpG,gBAAM,SAAS,CAAC,IAAI,CAAC,MAAM,SAAS;AAAA,QACtC;AACA,YAAI,SAAS,aAAa,KAAK,aAAa,SAAS;AACnD,gBAAM,IAAI,QAAQ;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,wBAAwB,QAAQ,YAAY,SAAS;AAC5D,QAAM,OAAO,CAAC,OAAO;AACrB,WAAS,OAAO,SAAS,SAAS,YAAY,SAAS,GAAG,UAAU,GAAG;AACrE,KAAC,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,MAAM;AAChC,SAAK,KAAK,IAAI;AAAA,EAChB;AACA,SAAO,KAAK,QAAQ;AACtB;AACA,SAAS,wBAAwB,OAAO,YAAY,WAAW,SAAS;AACtE,SAAO,wBAAwB,oBAAoB,OAAO,YAAY,WAAW,OAAO,GAAG,YAAY,OAAO;AAChH;AACA,SAAS,uBAAuB,QAAQ,SAAS,SAAS,mBAAmB,oBAAoB;AAC/F,SAAO,CAAC,SAAS;AACf,UAAM,UAAU,CAAC;AACjB,UAAM,eAAe,qBAAqB;AAC1C,QAAI,SAAS,oBAAoB,MAAM,OAAO,IAAI,CAAC,IAAI,IAAI;AAC3D,aAAS,IAAI,OAAO,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG;AACpD,cAAQ,KAAK,CAAC,IAAI,qBAAqB,WAAW,CAAC,CAAC;AACpD,UAAI,SAAS,gBAAgB,MAAM,OAAO,QAAQ;AAChD;AAAA,MACF;AACA,gBAAU,oBAAoB,MAAM,OAAO,CAAC,CAAC,IAAI,UAAU,IAAI;AAAA,IACjE;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,kBAAkB,MAAM,QAAQ,SAAS,SAAS,gBAAgB,aAAa,eAAe;AACrG,QAAM,SAAS,CAAC;AAChB,QAAM,aAAa,cAAc,OAAO,CAAC,OAAO,gBAAgB,QAAQ,aAAa,CAAC;AACtF,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG;AAC3C,UAAM,SAAS,OAAO,IAAI,CAAC,OAAO,WAAW,EAAE,OAAO,MAAM,EAAE,EAAE,MAAM,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;AAC1F,UAAM,eAAe,cAAc;AAAA,MACjC,CAAC,OAAO,aAAa,UAAU,SAAS,YAAY,CAAC,IAAI,YAAY,KAAK,KAAK;AAAA,MAC/E;AAAA,IACF;AACA,UAAM,eAAe,kBAAkB,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,UAAU,gBAAgB,cAAc,CAAC,IAAI;AACzI,WAAO,KAAK;AAAA,MACV,QAAQ,OAAO,IAAI,CAAC,EAAE,OAAO,MAAM,OAAO;AAAA,QACxC;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,QAAQ,cAAc,MAAM,KAAK;AAAA,MACnC,EAAE;AAAA,MACF,WAAW,EAAE,cAAc,aAAa,cAAc,CAAC,EAAE;AAAA,IAC3D,CAAC;AAAA,EACH;AACA,SAAO,EAAE,QAAQ,WAAW,EAAE,WAAW,EAAE;AAC7C;AACA,SAAS,oBAAoB,QAAQ,SAAS,SAAS,gBAAgB,mBAAmB,SAAS;AACjG,QAAM,cAAc,CAAC;AACrB,QAAM,gBAAgB,CAAC;AACvB,MAAI,OAAO,UAAU,SAAS;AAC5B,UAAM,eAAe,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,OAAO,SAAS;AAChH,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,kBAAY,CAAC,IAAI,IAAI;AACrB,oBAAc,CAAC,IAAI,IAAI,OAAO,SAAS,MAAM,OAAO,CAAC,CAAC,IAAI;AAAA,IAC5D;AACA,WAAO;AAAA,MACL,MAAM,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,CAAC,GAAG,UAAU,KAAK,IAAI,OAAO,OAAO,MAAM,CAAC;AAAA,MAChF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,sBAAsB,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,oBAAoB,MAAM,KAAK,GAAG,CAAC,IAAI,WAAW,OAAO,SAAS,WAAW,IAAI,UAAU,OAAO,UAAU;AAC5K,QAAM,eAAe,uBAAuB,QAAQ,SAAS,SAAS,mBAAmB,kBAAkB;AAC3G,QAAM,OAAO,wBAAwB,cAAc,SAAS,GAAG,OAAO,MAAM;AAC5E,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG;AAC3C,UAAM,SAAS,OAAO,MAAM,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;AAChD,gBAAY,CAAC,IAAI,WAAW,OAAO,SAAS,KAAK,IAAI,UAAU,OAAO;AACtE,kBAAc,CAAC,IAAI,IAAI,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,IAAI,MAAM,KAAK,GAAG,CAAC;AAAA,EAChF;AACA,SAAO,kBAAkB,MAAM,QAAQ,SAAS,SAAS,gBAAgB,aAAa,aAAa;AACrG;AACA,SAAS,qBAAqB,QAAQ,SAAS,SAAS,gBAAgB,SAAS;AAC/E,QAAM,qBAAqB,iBAAiB,WAAW,UAAU,KAAK,IAAI,UAAU,WAAW;AAC/F,QAAM,EAAE,QAAQ,UAAU,IAAI;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,OAAO,KAAK,CAAC,UAAU,MAAM,OAAO,KAAK,CAAC,EAAE,OAAO,OAAO,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAC,GAAG;AAC7F,WAAO,UAAU,IAAI,qBAAqB,QAAQ,SAAS,SAAS,gBAAgB,UAAU,CAAC,IAAI;AAAA,EACrG;AACA,SAAO,EAAE,QAAQ,SAAS,SAAS,gBAAgB,WAAW,EAAE,SAAS,GAAG,UAAU,EAAE;AAC1F;;;AFjHA,SAAS,kBAAkB,EAAE,QAAQ,aAAAC,cAAa,uBAAuB,GAAG,KAAK,GAAG,KAAK;AACvF,QAAM,EAAE,cAAc,eAAe,IAAI,kBAAkB,KAAKA,cAAa,qBAAqB;AAClG,QAAM,EAAE,SAAS,SAAS,SAAS,GAAG,UAAU,IAAI,oBAAoB,gBAAgB,EAAE,QAAQ,GAAG,KAAK,CAAC;AAC3G,QAAM,YAAQ;AAAA,IACZ,MAAM,mBAAmB,UAAU,YAAY,UAAU,YAAY,UAAU,YAAY,SAAS,qBAAqB,QAAQ,SAAS,SAAS,gBAAgB,OAAO,IAAI;AAAA,IAC9K,CAAC,QAAQ,SAAS,SAAS,gBAAgB,OAAO;AAAA,EACpD;AACA,aAAO,yBAAI,oBAAkB,EAAE,QAAQ,WAAW,KAAK,cAAc,OAAO,GAAG,UAAU,CAAC;AAC5F;AACA,IAAM,0BAAsB,0BAAW,iBAAiB;;;AGfxD,IAAAC,sBAAoB;AACpB,IAAAC,gBAAoC;;;ACDpC,SAAS,oBAAoB,gBAAgB,EAAE,SAAS,GAAG,KAAK,GAAG;AACjE,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG,mBAAmB,gBAAgB,IAAI;AAAA,IAC1C,SAAS,2BAA2B,SAAS,gBAAgB,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AAAA,EAC9E;AACF;;;ACNA,SAAS,qBAAqB,QAAQ,SAAS,SAAS,gBAAgB,SAAS;AAC/E,QAAM,eAAe,iBAAiB,WAAW,UAAU,KAAK,IAAI,UAAU,WAAW;AACzF,MAAI,eAAe,GAAG;AACpB,WAAO,UAAU,IAAI,qBAAqB,QAAQ,SAAS,SAAS,gBAAgB,UAAU,CAAC,IAAI;AAAA,EACrG;AACA,QAAM,6BAA6B,CAAC;AACpC,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,+BAA2B,CAAC,IAAI;AAAA,EAClC;AACA,QAAM,eAAe,OAAO;AAAA,IAC1B,CAAC,OAAO,OAAO,UAAU;AACvB,YAAM,iBAAiB,2BAA2B;AAAA,QAChD,CAAC,uBAAuB,MAAM,MAAM,OAAO,2BAA2B,qBAAqB,IAAI,IAAI,IAAI;AAAA,QACvG;AAAA,MACF;AACA,iCAA2B,cAAc,IAAI,2BAA2B,cAAc,IAAI,cAAc,MAAM,KAAK,IAAI,UAAU,IAAI;AACrI,YAAM,cAAc,EAAE,KAAK,EAAE,OAAO,MAAM,CAAC;AAC3C,aAAO;AAAA,IACT;AAAA,IACA,MAAM,KAAK,EAAE,QAAQ,QAAQ,GAAG,MAAM,CAAC,CAAC;AAAA,EAC1C;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,EAAE,QAAQ;AAAA,IACrB,QAAQ,aAAa,IAAI,CAAC,YAAY;AAAA,MACpC,QAAQ,OAAO,IAAI,CAAC,EAAE,OAAO,MAAM,OAAO;AAAA,QACxC;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,QAAQ,cAAc,MAAM,KAAK;AAAA,MACnC,EAAE;AAAA,IACJ,EAAE;AAAA,EACJ;AACF;;;AF7BA,SAAS,kBAAkB,EAAE,QAAQ,aAAAC,cAAa,uBAAuB,GAAG,KAAK,GAAG,KAAK;AACvF,QAAM,EAAE,cAAc,eAAe,IAAI,kBAAkB,KAAKA,cAAa,qBAAqB;AAClG,QAAM,EAAE,SAAS,SAAS,SAAS,GAAG,UAAU,IAAI,oBAAoB,gBAAgB,EAAE,QAAQ,GAAG,KAAK,CAAC;AAC3G,QAAM,YAAQ;AAAA,IACZ,MAAM,mBAAmB,UAAU,YAAY,UAAU,YAAY,UAAU,YAAY,SAAS,qBAAqB,QAAQ,SAAS,SAAS,gBAAgB,OAAO,IAAI;AAAA,IAC9K,CAAC,QAAQ,SAAS,SAAS,gBAAgB,OAAO;AAAA,EACpD;AACA,aAAO,yBAAI,oBAAkB,EAAE,QAAQ,WAAW,KAAK,cAAc,OAAO,GAAG,UAAU,CAAC;AAC5F;AACA,IAAM,0BAAsB,0BAAW,iBAAiB;;;AVVxD,SAAS,WAAW,EAAE,QAAQ,GAAG,KAAK,GAAG,KAAK;AAC5C,MAAI,WAAW,OAAQ,YAAO,yBAAI,kBAAgB,EAAE,KAAK,GAAG,KAAK,CAAC;AAClE,MAAI,WAAW,UAAW,YAAO,yBAAI,qBAAmB,EAAE,KAAK,GAAG,KAAK,CAAC;AACxE,MAAI,WAAW,UAAW,YAAO,yBAAI,qBAAmB,EAAE,KAAK,GAAG,KAAK,CAAC;AACxE,SAAO;AACT;AACA,IAAM,mBAAe,0BAAW,UAAU;", "names": ["import_jsx_runtime", "import_react", "import_jsx_runtime", "import_react", "breakpoints", "import_react", "breakpoints", "import_jsx_runtime", "import_react", "breakpoints", "import_jsx_runtime", "import_react", "breakpoints"]}