# Kế hoạch triển khai màn hình quản lý customer

## Tổng quan
Màn hình quản lý người dùng (customer) sẽ cho phép quản trị viên xem, thê<PERSON>, sửa và xóa thông tin người dùng (customer) trong hệ thống. Màn hình này sẽ được phát triển dựa trên màn hình quản lý bất động sản hiện có.

## Các tính năng chính
1. **Khu vực bộ lọc**: Lọc customer theo email, tên và số điện thoại
2. **Bảng dữ liệu**: Hiển thị danh sách customer với phân trang và sắp xếp
4. **Quản lý customer**: Cá<PERSON> tùy chọn chỉnh sửa, active/deactive, xem chi tiết và xóa cho mỗi customer
5. **Xem chi tiết thông tin profile và lịch sử và các số liệu của customer**: L<PERSON>y danh sách vai trò từ API và cho phép gán vai trò cho customer

## Cấu trúc API
Các API endpoint sẽ được sử dụng: chi tiết trong `api-document-swagger.jcon`
- `GET /api/customer/customers`: Lấy danh sách customer với bộ lọc và phân trang
- `GET /api/customer/{id}`: Lấy thông tin chi tiết của customer
- `GET /api/customer/dashboard/:customerId`: Lấy thông tin, số liệu hoạt động của customer
- `GET /api/customer/:id/invoice-info`: Lấy thông tin xuất hóa đơn của customer
- `PUT /api/customer/{customerId}/status`: Cập nhật trạng thái customer (kích hoạt/vô hiệu hóa)

## Cấu trúc thư mục
Các tệp mới sẽ được tạo theo cấu trúc sau:

- **Types:** `src/lib/types/customer.ts` - Chứa các interface TypeScript cho Customer và các model liên quan
- **Features:**
  - `src/features/customer-management/` - Thư mục chính cho tính năng quản lý customer
    - `components/` - Các components riêng cho tính năng customer
      - `customer-table.tsx`: Bảng dữ liệu hiển thị danh sách customer
      - `customer-filter.tsx`: Component bộ lọc tìm kiếm customer
      - `customer-detail.tsx`: Component hiển thị chi tiết thông tin customer
    - `hooks/use-customer.ts` - Custom hooks cho tính năng customer (nếu cần)
- **Services:** `src/services/customer-service.ts` - Các hàm để tương tác với API của customer
- **Routes/Pages:**
  - `src/routes/_authenticated/customer/index.tsx`: Trang chính liệt kê danh sách customer
  - `src/routes/_authenticated/customer/$customerId.tsx`: Trang xem chi tiết customer

## Giao diện người dùng

### 1. Khu vực bộ lọc
- Các trường tìm kiếm: Email, Tên, Số điện thoại
- Nút "Áp dụng bộ lọc" để gửi yêu cầu tìm kiếm

### 2. Bảng dữ liệu customer
- Các cột: Họ tên, Email, Số điện thoại, Loại người dùng, Trạng thái, Ngày tạo, Hành động
- Phân trang với hiển thị số trang và điều hướng
- Sắp xếp theo các cột
- Sử dụng `@tanstack/react-table` cho bảng dữ liệu.
- Bảng sử dụng ShadcnUI table trong folder `src/components/ui/table.tsx` để render UI

### 4. Modal chỉnh sửa/xem chi tiết customer - modal với kích thước lớn chiếm 95% màn hình, có thể scroll nội dung nếu chiều cao lớn hơn chiều cao hiển thị của màn hình
- Hiển thị thông tin chi tiết của customer
- Chế độ xem chi tiết: Hiển thị thông tin cùng nút "active/deactive" customer
- Nút "Đóng" để đóng modal

### 5. Modal xác nhận xóa
- Thông báo cảnh báo về việc xóa vĩnh viễn
- Nút "Xác nhận xóa" để thực hiện xóa
- Nút "Thoát" để hủy và đóng modal

## Luồng làm việc

### Hiển thị danh sách customer
1. Tải dữ liệu customer từ API với các tham số mặc định
2. Hiển thị dữ liệu trong bảng với phân trang

### Lọc danh sách customer
1. Người dùng nhập các tiêu chí tìm kiếm
2. Khi nhấn "Áp dụng bộ lọc", gửi yêu cầu API với các tham số tìm kiếm
3. Cập nhật bảng dữ liệu với kết quả trả về


### Chỉnh sửa/Xem chi tiết customer
1. Người dùng nhấn vào tùy chọn "Xem chi tiết" từ menu dropdown
2. Lấy thông tin chi tiết của customer từ API `/api/User/customers`
3. Lấy thông tin xuất hóa đơn của customer từ API `/api/User/:id/invoice-info`
3. Lấy hoạt động và dữ liệu của customer từ API `/api/User/dashboard/:userId`
4. Hiển thị modal với thông tin customer

### Xóa customer
1. Người dùng nhấn vào tùy chọn "Xóa" từ menu dropdown
2. Hiển thị modal xác nhận xóa
3. Nếu người dùng xác nhận, gửi yêu cầu API để xóa customer
4. Sau khi xóa thành công, làm mới danh sách customer

## Kế hoạch triển khai
1. Tạo file `src/lib/types/customer.ts` cho các định nghĩa types
2. Tạo thư mục `src/features/customer-management` và cấu trúc con
3. Tạo file `src/services/customer-service.ts` cho các services API
4. Triển khai các components cho tính năng:
   - Bảng danh sách customer
   - Form tìm kiếm
   - Modal chỉnh sửa/xem chi tiết
   - Modal xác nhận xóa
5. Tạo các trang routes trong `src/routes/_authenticated/customer/`
6. Tích hợp với hệ thống định tuyến và quản lý trạng thái
7. Kiểm tra và tối ưu hóa hiệu suất

## Lưu ý
- Đảm bảo xác thực và phân quyền phù hợp
- Đảm bảo phân chia component và sử dụng các best practice để bảo đảm performance của UI
- Xử lý lỗi và hiển thị thông báo phù hợp
- Đảm bảo giao diện người dùng nhất quán với phần còn lại của ứng dụng 