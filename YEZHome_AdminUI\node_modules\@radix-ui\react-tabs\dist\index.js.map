{"version": 3, "sources": ["../src/index.ts", "../src/tabs.tsx"], "sourcesContent": ["'use client';\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n} from './tabs';\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps } from './tabs';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA,cAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,uBAAqC;AACrC,2BAAmC;AACnC,gCAA4C;AAC5C,4BAAyB;AACzB,6BAA0B;AAC1B,uBAAkC;AAClC,6BAA6B;AAC7B,0CAAqC;AACrC,sBAAsB;AAoFd;AA5ER,IAAM,YAAY;AAGlB,IAAM,CAAC,mBAAmB,eAAe,QAAI,yCAAmB,WAAW;AAAA,EACzE;AACF,CAAC;AACD,IAAM,+BAA2B,uDAA4B;AAW7D,IAAM,CAAC,cAAc,cAAc,IAAI,kBAAoC,SAAS;AA6BpF,IAAM,OAAa;AAAA,EACjB,CAAC,OAA+B,iBAAiB;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,MACjB,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,gBAAY,qCAAa,GAAG;AAClC,UAAM,CAAC,OAAO,QAAQ,QAAI,0DAAqB;AAAA,MAC7C,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa,gBAAgB;AAAA,MAC7B,QAAQ;AAAA,IACV,CAAC;AAED,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP,YAAQ,uBAAM;AAAA,QACd;AAAA,QACA,eAAe;AAAA,QACf;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QAEA;AAAA,UAAC,iCAAU;AAAA,UAAV;AAAA,YACC,KAAK;AAAA,YACL,oBAAkB;AAAA,YACjB,GAAG;AAAA,YACJ,KAAK;AAAA;AAAA,QACP;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,KAAK,cAAc;AAMnB,IAAM,gBAAgB;AAOtB,IAAM,WAAiB;AAAA,EACrB,CAAC,OAAmC,iBAAiB;AACnD,UAAM,EAAE,aAAa,OAAO,MAAM,GAAG,UAAU,IAAI;AACnD,UAAM,UAAU,eAAe,eAAe,WAAW;AACzD,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,WACE;AAAA,MAAkB;AAAA,MAAjB;AAAA,QACC,SAAO;AAAA,QACN,GAAG;AAAA,QACJ,aAAa,QAAQ;AAAA,QACrB,KAAK,QAAQ;AAAA,QACb;AAAA,QAEA;AAAA,UAAC,iCAAU;AAAA,UAAV;AAAA,YACC,MAAK;AAAA,YACL,oBAAkB,QAAQ;AAAA,YACzB,GAAG;AAAA,YACJ,KAAK;AAAA;AAAA,QACP;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,SAAS,cAAc;AAMvB,IAAM,eAAe;AAQrB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,aAAa,OAAO,WAAW,OAAO,GAAG,aAAa,IAAI;AAClE,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,aAAa,UAAU,QAAQ;AACrC,WACE;AAAA,MAAkB;AAAA,MAAjB;AAAA,QACC,SAAO;AAAA,QACN,GAAG;AAAA,QACJ,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QAER;AAAA,UAAC,iCAAU;AAAA,UAAV;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,iBAAe;AAAA,YACf,iBAAe;AAAA,YACf,cAAY,aAAa,WAAW;AAAA,YACpC,iBAAe,WAAW,KAAK;AAAA,YAC/B;AAAA,YACA,IAAI;AAAA,YACH,GAAG;AAAA,YACJ,KAAK;AAAA,YACL,iBAAa,uCAAqB,MAAM,aAAa,CAAC,UAAU;AAG9D,kBAAI,CAAC,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,OAAO;AAC9D,wBAAQ,cAAc,KAAK;AAAA,cAC7B,OAAO;AAEL,sBAAM,eAAe;AAAA,cACvB;AAAA,YACF,CAAC;AAAA,YACD,eAAW,uCAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,CAAC,KAAK,OAAO,EAAE,SAAS,MAAM,GAAG,EAAG,SAAQ,cAAc,KAAK;AAAA,YACrE,CAAC;AAAA,YACD,aAAS,uCAAqB,MAAM,SAAS,MAAM;AAGjD,oBAAM,wBAAwB,QAAQ,mBAAmB;AACzD,kBAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;AACrD,wBAAQ,cAAc,KAAK;AAAA,cAC7B;AAAA,YACF,CAAC;AAAA;AAAA,QACH;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,eAAe;AAarB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,aAAa,OAAO,YAAY,UAAU,GAAG,aAAa,IAAI;AACtE,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,aAAa,UAAU,QAAQ;AACrC,UAAM,+BAAqC,aAAO,UAAU;AAE5D,IAAM,gBAAU,MAAM;AACpB,YAAM,MAAM,sBAAsB,MAAO,6BAA6B,UAAU,KAAM;AACtF,aAAO,MAAM,qBAAqB,GAAG;AAAA,IACvC,GAAG,CAAC,CAAC;AAEL,WACE,4CAAC,kCAAS,SAAS,cAAc,YAC9B,WAAC,EAAE,QAAQ,MACV;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACC,cAAY,aAAa,WAAW;AAAA,QACpC,oBAAkB,QAAQ;AAAA,QAC1B,MAAK;AAAA,QACL,mBAAiB;AAAA,QACjB,QAAQ,CAAC;AAAA,QACT,IAAI;AAAA,QACJ,UAAU;AAAA,QACT,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA,UACL,GAAG,MAAM;AAAA,UACT,mBAAmB,6BAA6B,UAAU,OAAO;AAAA,QACnE;AAAA,QAEC,qBAAW;AAAA;AAAA,IACd,GAEJ;AAAA,EAEJ;AACF;AAEA,YAAY,cAAc;AAI1B,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,IAAMC,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;", "names": ["Root", "Root"]}