using System.Text;
using System.Xml.Linq;

namespace RealEstate.Domain.Entities
{
    public class BlogPost : BaseEntityWithAuditable
    {
        public Guid AuthorID { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string FeaturedImage { get; set; }
        public string Tags { get; set; }
        public string Status { get; set; }
        public string Slug { get; set; }
        public bool IsFeature { get; set; }
        public DateTime? PublishedAt { get; set; }       

        public AppUser Author { get; set; }
        public ICollection<BlogComment> BlogComments { get; set; }

        public void GenerateSlugFromName()
        {
            if (!string.IsNullOrWhiteSpace(Title))
            {
                // Normalize Vietnamese characters
                string normalized = Title.Normalize(NormalizationForm.FormD);
                var vietnameseRemoved = new string(normalized
                    .Where(c => System.Globalization.CharUnicodeInfo.GetUnicodeCategory(c) != System.Globalization.UnicodeCategory.NonSpacingMark)
                    .ToArray());

                // Generate slug
                Slug = $"{vietnameseRemoved
                    .ToLowerInvariant()
                    .Replace(" ", "-")
                    .Replace("--", "-")
                    .Trim('-')}-{DateTime.Now.ToString("ddMMyyhhmmss")}";
            }
        }
    }
}