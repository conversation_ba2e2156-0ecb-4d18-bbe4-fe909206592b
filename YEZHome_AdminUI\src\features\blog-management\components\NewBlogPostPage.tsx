import { useNavigate } from "@tanstack/react-router";
import { useMutation } from "@tanstack/react-query";
import { blogService } from "@/services/blog-service";
import { BlogPostForm } from "@/features/blog-management/components/blog-post-form";
import type { BlogFormValues } from "@/features/blog-management/components/blog-post-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/store/auth-store";
import { toast } from "@/components/ui/use-toast";

export function NewBlogPostPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();

  const createMutation = useMutation({
    mutationFn: (data: BlogFormValues) => {
      // Thêm authorID từ user hiện tại
      return blogService.createBlogPost({
        ...data,
        authorID: user?.id || "",
      });
    },
    onSuccess: () => {
      toast({
        title: "Thành công",
        description: "Bài viết đã được tạo thành công.",
        variant: "success",
      });
      navigate({ to: "/blog" });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể tạo bài viết. Vui lòng thử lại sau.",
        variant: "destructive",
      });
      console.error("Error creating blog post:", error);
    },
  });

  const handleSubmit = (data: BlogFormValues) => {
    createMutation.mutate(data);
  };

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <CardTitle>Tạo bài viết mới</CardTitle>
        </CardHeader>
        <CardContent>
          <BlogPostForm
            onSubmit={handleSubmit}
            isSubmitting={createMutation.isPending}
          />
        </CardContent>
      </Card>
    </div>
  );
} 