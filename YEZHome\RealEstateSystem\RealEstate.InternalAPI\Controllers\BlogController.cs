﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.CustomModel;

namespace RealEstate.InternalAPI.Controllers
{
    [Route("api/[controller]")]
    public class BlogController : BaseController
    {
        private readonly IBlogService _blogPostService;

        public BlogController(IBlogService blogPostService)
        {
            _blogPostService = blogPostService;
        }

        [HttpGet("blog-posts")]
        public async Task<IActionResult> GetBlogPosts([FromQuery] PagingRequest request, [FromQuery] string? title)
        {
            try
            {
                var result = await _blogPostService.GetBlogAsync(request, title ?? string.Empty);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "An error occurred while retrieving blog posts. Please try again later." });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<BlogPostDto>> GetBlogPostById(Guid id)
        {
            var blogPost = await _blogPostService.GetBlogByIdAsync(id);
            if (blogPost == null)
            {
                return NotFound();
            }
            return Ok(blogPost);
        }

        [HttpPost]
        public async Task<ActionResult<BlogPostDto>> CreateBlog(CreateBlogPostDto blogDto)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest("User không hợp lệ");
            }

            var createdBlog = await _blogPostService.CreateBlogAsync(blogDto, userId.Value);
            return CreatedAtAction(nameof(GetBlogPostById), new { id = createdBlog.Id }, createdBlog);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBlog(Guid id, CreateBlogPostDto blogDto)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("User không hợp lệ");
                }

                await _blogPostService.UpdateBlogAsync(id, blogDto, userId.Value);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBlog(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("User không hợp lệ");
                }

                await _blogPostService.DeleteBlogAsync(id, userId.Value);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            return NoContent();
        }
    }
}
