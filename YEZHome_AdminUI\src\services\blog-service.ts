import apiClient from './axios-config';
import type { PagedBlogPosts, BlogPost, CreateBlogPost } from '@/lib/types/blog';

interface BlogPostsParams {
  PageNumber?: number;
  PageSize?: number;
  SearchQuery?: string;
  SortColumn?: string;
  SortDescending?: boolean;
  Status?: string;
  AuthorId?: string;
  FromDate?: string;
  ToDate?: string;
}

const getBlogPosts = async (params: BlogPostsParams): Promise<PagedBlogPosts> => {
  const response = await apiClient.get('/Blog/blog-posts', { params });
  return response.data;
};

const getBlogPost = async (id: string): Promise<BlogPost> => {
  const response = await apiClient.get(`/Blog/${id}`);
  return response.data;
};

const createBlogPost = async (data: CreateBlogPost): Promise<BlogPost> => {
  const response = await apiClient.post('/Blog', data);
  return response.data;
};

const updateBlogPost = async (id: string, data: CreateBlogPost): Promise<void> => {
  await apiClient.put(`/Blog/${id}`, data);
};

const deleteBlogPost = async (id: string): Promise<void> => {
  await apiClient.delete(`/Blog/${id}`);
};

export const blogService = {
  getBlogPosts,
  getBlogPost,
  createBlogPost,
  updateBlogPost,
  deleteBlogPost,
}; 